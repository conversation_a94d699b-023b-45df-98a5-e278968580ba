<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basketball Risk Filter - 7-10 Game Ticket Optimizer</title>
    <script crossorigin src="https://unpkg.com/react@17/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        #root { max-width: 1400px; margin: 0 auto; }
        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .grid { display: grid; }
        .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
        .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
        .gap-4 { gap: 1rem; }
        .gap-6 { gap: 1.5rem; }
        .p-4 { padding: 1rem; }
        .p-6 { padding: 1.5rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-4 { margin-top: 1rem; }
        .text-center { text-align: center; }
        .text-sm { font-size: 0.875rem; }
        .text-lg { font-size: 1.125rem; }
        .text-xl { font-size: 1.25rem; }
        .text-2xl { font-size: 1.5rem; }
        .font-bold { font-weight: 700; }
        .font-medium { font-weight: 500; }
        .rounded-lg { border-radius: 0.5rem; }
        .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
        .w-full { width: 100%; }

        /* Colors */
        .bg-white { background-color: white; }
        .bg-gray-50 { background-color: #f9fafb; }
        .bg-gray-100 { background-color: #f3f4f6; }
        .bg-blue-50 { background-color: #eff6ff; }
        .bg-green-50 { background-color: #f0fdf4; }
        .bg-yellow-50 { background-color: #fefce8; }
        .bg-red-50 { background-color: #fef2f2; }
        .bg-purple-50 { background-color: #faf5ff; }

        .text-gray-600 { color: #4b5563; }
        .text-gray-700 { color: #374151; }
        .text-blue-600 { color: #2563eb; }
        .text-green-600 { color: #16a34a; }
        .text-yellow-600 { color: #ca8a04; }
        .text-red-600 { color: #dc2626; }
        .text-purple-600 { color: #9333ea; }
        .text-white { color: white; }

        .border { border: 1px solid #e5e7eb; }
        .border-green-200 { border-color: #bbf7d0; }
        .border-yellow-200 { border-color: #fef3c7; }
        .border-red-200 { border-color: #fecaca; }

        input, select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.25rem;
            width: 100%;
        }

        .risk-safe {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            border: 2px solid #16a34a;
        }
        .risk-caution {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 2px solid #ca8a04;
        }
        .risk-avoid {
            background: linear-gradient(135deg, #fecaca, #fca5a5);
            border: 2px solid #dc2626;
        }

        .confidence-bar {
            height: 12px;
            border-radius: 6px;
            background-color: #e5e7eb;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .alert-box {
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            font-weight: 600;
        }

        .alert-safe {
            background-color: #dcfce7;
            color: #166534;
            border-left: 4px solid #16a34a;
        }

        .alert-caution {
            background-color: #fef3c7;
            color: #92400e;
            border-left: 4px solid #ca8a04;
        }

        .alert-avoid {
            background-color: #fecaca;
            color: #991b1b;
            border-left: 4px solid #dc2626;
        }

        @media (min-width: 768px) {
            .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
            .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        function BasketballRiskFilter() {
            // Using your exact data structure from all-in-one
            const [teamA, setTeamA] = React.useState({
                name: "Team A",
                offense: 83.6,        // Season average PPG
                defense: 85.8,        // Season average points allowed
                recentOffense: 84.2,  // Last 10 games average points scored
                offensiveVariance: 5.2, // Standard deviation of points scored
                homeAdvantage: true,
                restDays: 2
            });

            const [teamB, setTeamB] = React.useState({
                name: "Team B",
                offense: 81.9,        // Season average PPG
                defense: 80.9,        // Season average points allowed
                recentOffense: 82.5,  // Last 10 games average points scored
                offensiveVariance: 4.8, // Standard deviation of points scored
                homeAdvantage: false,
                restDays: 1
            });

            const [bookieLine, setBookieLine] = React.useState(81.5);
            const [h2hAvgTotal, setH2hAvgTotal] = React.useState("");

            // Your exact constants from all-in-one
            const LEAGUE_AVG_PACE = 97.5;
            const LEAGUE_AVG_PPG = 110.6;
            const LEAGUE_REGRESSION_WEIGHT = 0.2;

            // Your exact calculateOffensiveVariance function
            const calculateOffensiveVariance = (seasonAvg, recentAvg) => {
                if (!seasonAvg || !recentAvg) {
                    return 5.0;
                }
                const difference = Math.abs(recentAvg - seasonAvg);
                let variance = 3.0;
                if (difference > 5) {
                    variance += 4.0;
                } else if (difference > 3) {
                    variance += 2.5;
                } else if (difference > 1) {
                    variance += 1.0;
                }
                return Math.min(Math.max(variance, 2.0), 8.0);
            };

            // Your exact calculateAdvancedPace function
            const calculateAdvancedPace = (team) => {
                const offensiveEfficiency = team.offense / LEAGUE_AVG_PPG;
                const defensiveEfficiency = team.defense / LEAGUE_AVG_PPG;
                const efficiencyPaceFactor = (offensiveEfficiency + defensiveEfficiency) / 2;

                const recentGamesWeight = 0.75;
                const recentFormRatio = team.recentOffense / team.offense;
                const bayesianRecentForm = (recentFormRatio * recentGamesWeight + 1 * (1 - recentGamesWeight));

                const regressionComponent = LEAGUE_REGRESSION_WEIGHT * LEAGUE_AVG_PACE;
                const teamComponent = (1 - LEAGUE_REGRESSION_WEIGHT) * (
                    LEAGUE_AVG_PACE * efficiencyPaceFactor * bayesianRecentForm
                );

                const calculatedPace = regressionComponent + teamComponent;
                const minPace = LEAGUE_AVG_PACE * 0.85;
                const maxPace = LEAGUE_AVG_PACE * 1.15;

                return Math.max(minPace, Math.min(maxPace, calculatedPace));
            };

            // Calculate all your existing metrics
            const teamAPace = calculateAdvancedPace(teamA);
            const teamBPace = calculateAdvancedPace(teamB);
            const teamAVariance = calculateOffensiveVariance(teamA.offense, teamA.recentOffense);
            const teamBVariance = calculateOffensiveVariance(teamB.offense, teamB.recentOffense);

            // Your existing prediction models
            const basicFullGameTotal = (teamA.offense + teamB.defense) / 2 + (teamB.offense + teamA.defense) / 2;
            const recentFormModel = (teamA.recentOffense + teamB.defense) / 2 + (teamB.recentOffense + teamA.defense) / 2;
            const paceAdjustedModel = basicFullGameTotal * ((teamAPace + teamBPace) / (2 * LEAGUE_AVG_PACE));

            // Your existing confidence calculation
            let confidenceScore = 0;
            const avgVariance = (teamAVariance + teamBVariance) / 2;
            if (avgVariance < 4) {
                confidenceScore += 2;
            } else if (avgVariance > 7) {
                confidenceScore -= 1;
            }

            const teamAFormDiff = Math.abs(teamA.recentOffense - teamA.offense);
            const teamBFormDiff = Math.abs(teamB.recentOffense - teamB.offense);
            if (teamAFormDiff < 2 && teamBFormDiff < 2) {
                confidenceScore += 2;
            } else if (teamAFormDiff > 5 || teamBFormDiff > 5) {
                confidenceScore -= 1;
            }

            if (h2hAvgTotal > 0) {
                confidenceScore += 2;
            }

            // Your existing ensemble model
            const ensembleTotal = (basicFullGameTotal * 0.3) + (recentFormModel * 0.5) + (paceAdjustedModel * 0.2);
            const firstHalfTotal = ensembleTotal / 2;
            const difference = firstHalfTotal - bookieLine;

            // ENHANCED RISK ASSESSMENT FOR 7-10 GAME TICKETS

            // 1. Model Consensus Risk (How much do predictions agree?)
            const predictions = [basicFullGameTotal/2, recentFormModel/2, paceAdjustedModel/2];
            const avgPrediction = predictions.reduce((a, b) => a + b, 0) / predictions.length;
            const maxDeviation = Math.max(...predictions.map(p => Math.abs(p - avgPrediction)));

            let consensusRisk = 0;
            if (maxDeviation > 4) consensusRisk = 8; // High risk
            else if (maxDeviation > 2) consensusRisk = 5; // Medium risk
            else consensusRisk = 2; // Low risk

            // 2. Variance Risk (High variance = unpredictable)
            let varianceRisk = 0;
            if (avgVariance > 6.5) varianceRisk = 8;
            else if (avgVariance > 5) varianceRisk = 5;
            else varianceRisk = 2;

            // 3. Form Inconsistency Risk
            const maxFormDiff = Math.max(teamAFormDiff, teamBFormDiff);
            let formRisk = 0;
            if (maxFormDiff > 6) formRisk = 8;
            else if (maxFormDiff > 4) formRisk = 5;
            else formRisk = 2;

            // 4. H2H Contradiction Risk
            let h2hRisk = 3; // Default medium risk when no H2H data
            if (h2hAvgTotal > 0) {
                const h2hHalfTotal = h2hAvgTotal / 2;
                const h2hDiff = Math.abs(h2hHalfTotal - firstHalfTotal);
                if (h2hDiff > 6) h2hRisk = 8;
                else if (h2hDiff > 3) h2hRisk = 5;
                else h2hRisk = 1;
            }

            // 5. Edge Size Risk (Small edges are risky)
            let edgeRisk = 0;
            const edgeSize = Math.abs(difference);
            if (edgeSize < 1) edgeRisk = 7;
            else if (edgeSize < 2) edgeRisk = 4;
            else edgeRisk = 1;

            // 6. Rest Disparity Risk
            const restDiff = Math.abs(teamA.restDays - teamB.restDays);
            let restRisk = 0;
            if (restDiff >= 2) restRisk = 6;
            else if (restDiff === 1) restRisk = 3;
            else restRisk = 1;

            // OVERALL RISK CALCULATION (weighted for your betting pattern)
            const riskFactors = [
                { name: "Model Consensus", risk: consensusRisk, weight: 0.25 },
                { name: "Team Variance", risk: varianceRisk, weight: 0.20 },
                { name: "Form Consistency", risk: formRisk, weight: 0.20 },
                { name: "H2H Contradiction", risk: h2hRisk, weight: 0.15 },
                { name: "Edge Size", risk: edgeRisk, weight: 0.15 },
                { name: "Rest Disparity", risk: restRisk, weight: 0.05 }
            ];

            const overallRisk = riskFactors.reduce((total, factor) =>
                total + (factor.risk * factor.weight), 0);

            // RISK CLASSIFICATION FOR 7-10 GAME TICKETS
            let riskLevel, riskClass, recommendation, alertClass;

            if (overallRisk <= 3) {
                riskLevel = "SAFE TO BET";
                riskClass = "risk-safe";
                recommendation = "✅ INCLUDE IN TICKET - Low risk game";
                alertClass = "alert-safe";
            } else if (overallRisk <= 5.5) {
                riskLevel = "USE WITH CAUTION";
                riskClass = "risk-caution";
                recommendation = "⚠️ LIMIT TO 2-3 PER TICKET - Medium risk";
                alertClass = "alert-caution";
            } else {
                riskLevel = "AVOID COMPLETELY";
                riskClass = "risk-avoid";
                recommendation = "❌ DO NOT BET - High risk of loss";
                alertClass = "alert-avoid";
            }

            // Input handlers (same as your all-in-one)
            const handleTeamAChange = (field, value) => {
                setTeamA({...teamA, [field]: typeof value === 'boolean' ? value : parseFloat(value) || 0});
            };

            const handleTeamBChange = (field, value) => {
                setTeamB({...teamB, [field]: typeof value === 'boolean' ? value : parseFloat(value) || 0});
            };

            return (
                <div className="flex flex-col gap-6">
                    <div className="text-center mb-6">
                        <h1 className="text-2xl font-bold mb-2">🎯 Basketball Risk Filter</h1>
                        <p className="text-gray-600">Optimize your 7-10 game tickets by filtering high-risk games</p>
                    </div>

                    {/* Risk Assessment Result */}
                    <div className={`p-6 rounded-lg shadow-md ${riskClass}`}>
                        <div className="text-center">
                            <h2 className="text-xl font-bold mb-2">RISK ASSESSMENT</h2>
                            <div className="text-2xl font-bold mb-4">{riskLevel}</div>
                            <div className="confidence-bar mb-4">
                                <div
                                    className={`confidence-fill ${
                                        overallRisk <= 3 ? 'bg-green-600' :
                                        overallRisk <= 5.5 ? 'bg-yellow-600' : 'bg-red-600'
                                    }`}
                                    style={{width: `${Math.min(overallRisk * 12, 100)}%`}}
                                ></div>
                            </div>
                            <div className="text-lg">Risk Score: {overallRisk.toFixed(1)}/10</div>
                        </div>
                    </div>

                    <div className={`alert-box ${alertClass}`}>
                        {recommendation}
                    </div>

                    {/* Team Input Forms - Same as your all-in-one */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Team A */}
                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <h3 className="text-lg font-bold mb-4 text-blue-600">Team A</h3>
                            <div className="grid gap-4">
                                <div>
                                    <label className="block text-sm font-medium mb-2">Team Name</label>
                                    <input
                                        type="text"
                                        value={teamA.name}
                                        onChange={(e) => handleTeamAChange('name', e.target.value)}
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Season Average PPG</label>
                                    <input
                                        type="number"
                                        value={teamA.offense}
                                        onChange={(e) => handleTeamAChange('offense', e.target.value)}
                                        step="0.1"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Season Average Points Allowed</label>
                                    <input
                                        type="number"
                                        value={teamA.defense}
                                        onChange={(e) => handleTeamAChange('defense', e.target.value)}
                                        step="0.1"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Last 10 Games Average PPG</label>
                                    <input
                                        type="number"
                                        value={teamA.recentOffense}
                                        onChange={(e) => handleTeamAChange('recentOffense', e.target.value)}
                                        step="0.1"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Offensive Variance (auto-calculated)</label>
                                    <input
                                        type="number"
                                        value={teamAVariance.toFixed(1)}
                                        readOnly
                                        style={{backgroundColor: '#f3f4f6'}}
                                    />
                                </div>
                                <div className="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        checked={teamA.homeAdvantage}
                                        onChange={(e) => {
                                            handleTeamAChange('homeAdvantage', e.target.checked);
                                            if (e.target.checked) handleTeamBChange('homeAdvantage', false);
                                        }}
                                    />
                                    <label className="text-sm font-medium">Home Team</label>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Rest Days</label>
                                    <select
                                        value={teamA.restDays}
                                        onChange={(e) => handleTeamAChange('restDays', e.target.value)}
                                    >
                                        <option value={0}>0 (Back-to-back)</option>
                                        <option value={1}>1 Day</option>
                                        <option value={2}>2 Days</option>
                                        <option value={3}>3+ Days</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Team B */}
                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <h3 className="text-lg font-bold mb-4 text-purple-600">Team B</h3>
                            <div className="grid gap-4">
                                <div>
                                    <label className="block text-sm font-medium mb-2">Team Name</label>
                                    <input
                                        type="text"
                                        value={teamB.name}
                                        onChange={(e) => handleTeamBChange('name', e.target.value)}
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Season Average PPG</label>
                                    <input
                                        type="number"
                                        value={teamB.offense}
                                        onChange={(e) => handleTeamBChange('offense', e.target.value)}
                                        step="0.1"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Season Average Points Allowed</label>
                                    <input
                                        type="number"
                                        value={teamB.defense}
                                        onChange={(e) => handleTeamBChange('defense', e.target.value)}
                                        step="0.1"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Last 10 Games Average PPG</label>
                                    <input
                                        type="number"
                                        value={teamB.recentOffense}
                                        onChange={(e) => handleTeamBChange('recentOffense', e.target.value)}
                                        step="0.1"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Offensive Variance (auto-calculated)</label>
                                    <input
                                        type="number"
                                        value={teamBVariance.toFixed(1)}
                                        readOnly
                                        style={{backgroundColor: '#f3f4f6'}}
                                    />
                                </div>
                                <div className="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        checked={teamB.homeAdvantage}
                                        onChange={(e) => {
                                            handleTeamBChange('homeAdvantage', e.target.checked);
                                            if (e.target.checked) handleTeamAChange('homeAdvantage', false);
                                        }}
                                    />
                                    <label className="text-sm font-medium">Home Team</label>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">Rest Days</label>
                                    <select
                                        value={teamB.restDays}
                                        onChange={(e) => handleTeamBChange('restDays', e.target.value)}
                                    >
                                        <option value={0}>0 (Back-to-back)</option>
                                        <option value={1}>1 Day</option>
                                        <option value={2}>2 Days</option>
                                        <option value={3}>3+ Days</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* H2H and Bookie Line */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <h3 className="text-lg font-bold mb-4">H2H Data (Optional)</h3>
                            <div>
                                <label className="block text-sm font-medium mb-2">Average Total Points (Last 10 H2H Games)</label>
                                <input
                                    type="number"
                                    value={h2hAvgTotal || ""}
                                    onChange={(e) => setH2hAvgTotal(parseFloat(e.target.value) || "")}
                                    placeholder="Leave empty if unknown"
                                    step="0.1"
                                />
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <h3 className="text-lg font-bold mb-4">Betting Information</h3>
                            <div>
                                <label className="block text-sm font-medium mb-2">Bookie First Half Line</label>
                                <input
                                    type="number"
                                    value={bookieLine}
                                    onChange={(e) => setBookieLine(parseFloat(e.target.value) || 0)}
                                    step="0.5"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Detailed Risk Breakdown */}
                    <div className="bg-white p-6 rounded-lg shadow-md">
                        <h3 className="text-lg font-bold mb-4">📊 Risk Factor Breakdown</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {riskFactors.map((factor, index) => {
                                const riskColor = factor.risk <= 3 ? 'text-green-600' :
                                                factor.risk <= 5 ? 'text-yellow-600' : 'text-red-600';
                                const bgColor = factor.risk <= 3 ? 'bg-green-600' :
                                              factor.risk <= 5 ? 'bg-yellow-600' : 'bg-red-600';

                                return (
                                    <div key={index} className="border rounded-lg p-4">
                                        <div className="flex justify-between items-center mb-2">
                                            <span className="font-medium">{factor.name}</span>
                                            <span className={`font-bold ${riskColor}`}>
                                                {factor.risk.toFixed(1)}/10
                                            </span>
                                        </div>
                                        <div className="confidence-bar mb-2">
                                            <div
                                                className={`confidence-fill ${bgColor}`}
                                                style={{width: `${factor.risk * 10}%`}}
                                            ></div>
                                        </div>
                                        <div className="text-xs text-gray-600">
                                            Weight: {(factor.weight * 100).toFixed(0)}%
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>

                    {/* Prediction Summary */}
                    <div className="bg-gray-50 p-6 rounded-lg">
                        <h3 className="text-lg font-bold mb-4">🎯 Prediction Summary</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">{firstHalfTotal.toFixed(1)}</div>
                                <div className="text-sm text-gray-600">Predicted 1H Total</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-purple-600">{bookieLine}</div>
                                <div className="text-sm text-gray-600">Bookie Line</div>
                            </div>
                            <div className="text-center">
                                <div className={`text-2xl font-bold ${difference > 0 ? 'text-red-600' : 'text-blue-600'}`}>
                                    {difference > 0 ? '+' : ''}{difference.toFixed(1)}
                                </div>
                                <div className="text-sm text-gray-600">Difference</div>
                            </div>
                        </div>
                    </div>

                    {/* Strategy Guide */}
                    <div className="bg-blue-50 p-6 rounded-lg">
                        <h3 className="text-lg font-bold mb-4 text-blue-800">📋 7-10 Game Ticket Strategy</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                            <div className="bg-green-50 p-4 rounded border-l-4 border-green-500">
                                <h4 className="font-bold text-green-700 mb-2">SAFE GAMES (≤3.0)</h4>
                                <ul className="space-y-1 text-green-600">
                                    <li>• Include freely in tickets</li>
                                    <li>• Low variance teams</li>
                                    <li>• Strong model consensus</li>
                                    <li>• Consistent recent form</li>
                                </ul>
                            </div>
                            <div className="bg-yellow-50 p-4 rounded border-l-4 border-yellow-500">
                                <h4 className="font-bold text-yellow-700 mb-2">CAUTION GAMES (3.1-5.5)</h4>
                                <ul className="space-y-1 text-yellow-600">
                                    <li>• Limit to 2-3 per ticket</li>
                                    <li>• Medium variance/risk</li>
                                    <li>• Some model disagreement</li>
                                    <li>• Use with strong games</li>
                                </ul>
                            </div>
                            <div className="bg-red-50 p-4 rounded border-l-4 border-red-500">
                                <h4 className="font-bold text-red-700 mb-2">AVOID GAMES (>5.5)</h4>
                                <ul className="space-y-1 text-red-600">
                                    <li>• Do not include in tickets</li>
                                    <li>• High variance/unpredictable</li>
                                    <li>• Conflicting predictions</li>
                                    <li>• Likely to cause losses</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Render the app
        ReactDOM.render(
            <React.StrictMode>
                <BasketballRiskFilter />
            </React.StrictMode>,
            document.getElementById('root')
        );
    </script>
</body>
</html>
