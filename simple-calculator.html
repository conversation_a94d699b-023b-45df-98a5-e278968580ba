<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basketball Pace Factor Calculator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 20px;
        }

        .toggle-switch {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .toggle-container {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-checkbox {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-label {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-label:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .toggle-checkbox:checked + .toggle-label {
            background-color: #2563eb;
        }

        .toggle-checkbox:checked + .toggle-label:before {
            transform: translateX(26px);
        }

        .toggle-text {
            margin-left: 10px;
        }

        .pace-info {
            background-color: #eff6ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #bfdbfe;
        }

        .pace-info h2 {
            color: #1e40af;
            margin-top: 0;
        }

        .pace-values {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .team-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .team-a {
            background-color: #dbeafe;
            padding: 15px;
            border-radius: 8px;
        }

        .team-b {
            background-color: #fee2e2;
            padding: 15px;
            border-radius: 8px;
        }

        .team-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .team-a h2 {
            color: #1e40af;
            margin: 0;
        }

        .team-b h2 {
            color: #991b1b;
            margin: 0;
        }

        .form-group {
            margin-bottom: 10px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .bookie-line {
            background-color: #f3e8ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .results {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
        }

        .results h3 {
            margin-top: 0;
        }

        .recommendation {
            margin-top: 15px;
            padding: 10px;
            background-color: #f9fafb;
            border-radius: 4px;
        }

        .recommendation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .value-badge {
            background-color: #2563eb;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 14px;
        }

        .recommendation-text {
            font-size: 18px;
            font-weight: bold;
            margin: 10px 0;
        }

        .over {
            color: #dc2626;
        }

        .under {
            color: #2563eb;
        }

        .edge-text {
            font-size: 12px;
            color: #6b7280;
        }

        @media (max-width: 768px) {
            .team-stats {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Basketball Over/Under Prediction Calculator</h1>

        <div class="toggle-switch">
            <div class="toggle-container">
                <input type="checkbox" id="advancedMode" class="toggle-checkbox">
                <label for="advancedMode" class="toggle-label"></label>
            </div>
            <span class="toggle-text">Use Enhanced Model</span>
        </div>

        <div id="paceInfo" class="pace-info" style="display: none;">
            <h2>Pace Factor Estimation</h2>
            <p>Pace is automatically calculated based on team offense, defense, and recent scoring.</p>
            <div class="pace-values">
                <div>Team A: <span id="teamAPace">97.5</span></div>
                <div>Team B: <span id="teamBPace">96.8</span></div>
                <div>League Avg: <span>97.5</span></div>
                <div>Impact: <span id="paceImpact">-0.4 pts</span></div>
            </div>
        </div>

        <div class="team-stats">
            <div class="team-a">
                <div class="team-header">
                    <h2>Team A Stats</h2>
                    <div>
                        <input type="checkbox" id="teamAHome" disabled>
                        <label for="teamAHome" style="display: inline;">Home Team</label>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="form-group">
                        <label for="teamAOffense">Season Offense (PPG):</label>
                        <input type="number" id="teamAOffense" value="83.6" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="teamADefense">Season Defense (PPG Allowed):</label>
                        <input type="number" id="teamADefense" value="85.8" step="0.1">
                    </div>

                    <div class="form-group advanced-field" style="display: none;">
                        <label for="teamARecentOffense">Last 10 Games Points Scored:</label>
                        <input type="number" id="teamARecentOffense" value="84.2" step="0.1">
                    </div>
                    <div class="form-group advanced-field" style="display: none;">
                        <label for="teamAOffVar">Offensive Variance (auto-calculated):</label>
                        <input type="number" id="teamAOffVar" value="5.2" step="0.1" readonly title="Standard deviation of points scored - calculated based on recent form">
                    </div>
                    <div class="form-group advanced-field" style="display: none;">
                        <label for="teamAPaceDisplay">Pace Factor (Auto-calculated):</label>
                        <input type="number" id="teamAPaceDisplay" value="97.5" readonly>
                    </div>
                    <div class="form-group advanced-field" style="display: none;">
                        <label for="teamARest">Days of Rest:</label>
                        <select id="teamARest">
                            <option value="0">0 (Back-to-Back)</option>
                            <option value="1">1 Day</option>
                            <option value="2" selected>2 Days</option>
                            <option value="3">3+ Days</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="team-b">
                <div class="team-header">
                    <h2>Team B Stats</h2>
                    <div>
                        <input type="checkbox" id="teamBHome" disabled>
                        <label for="teamBHome" style="display: inline;">Home Team</label>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="form-group">
                        <label for="teamBOffense">Season Offense (PPG):</label>
                        <input type="number" id="teamBOffense" value="81.9" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="teamBDefense">Season Defense (PPG Allowed):</label>
                        <input type="number" id="teamBDefense" value="80.9" step="0.1">
                    </div>

                    <div class="form-group advanced-field" style="display: none;">
                        <label for="teamBRecentOffense">Last 10 Games Points Scored:</label>
                        <input type="number" id="teamBRecentOffense" value="82.5" step="0.1">
                    </div>
                    <div class="form-group advanced-field" style="display: none;">
                        <label for="teamBOffVar">Offensive Variance (auto-calculated):</label>
                        <input type="number" id="teamBOffVar" value="4.8" step="0.1" readonly title="Standard deviation of points scored - calculated based on recent form">
                    </div>
                    <div class="form-group advanced-field" style="display: none;">
                        <label for="teamBPaceDisplay">Pace Factor (Auto-calculated):</label>
                        <input type="number" id="teamBPaceDisplay" value="96.8" readonly>
                    </div>
                    <div class="form-group advanced-field" style="display: none;">
                        <label for="teamBRest">Days of Rest:</label>
                        <select id="teamBRest">
                            <option value="0">0 (Back-to-Back)</option>
                            <option value="1" selected>1 Day</option>
                            <option value="2">2 Days</option>
                            <option value="3">3+ Days</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="h2h-section advanced-field" style="display: none; background-color: #e0f2fe; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #0369a1; margin-top: 0;">Head-to-Head History (Optional)</h2>
            <p style="margin-top: 0; margin-bottom: 10px; color: #0369a1;">Only fill this if you have H2H data available</p>
            <div class="form-group">
                <label for="h2hAvgTotal">Average Total Points (Last 10 H2H Games):</label>
                <input type="number" id="h2hAvgTotal" value="" placeholder="Leave empty if unknown" step="0.1">
            </div>

        </div>

        <div class="bookie-line">
            <div class="form-group">
                <label for="bookieLine">Bookie 1st Half Total Line:</label>
                <input type="number" id="bookieLine" value="81.5" step="0.5">
            </div>
        </div>

        <div class="results">
            <h3>Prediction Results</h3>
            <div>
                <div>Full Game Total: <span id="fullGameTotal">166.1</span></div>
                <div>First Half Total: <span id="firstHalfTotal">83.1</span></div>
                <div>Bookie Line: <span id="bookieLineDisplay">81.5</span></div>
                <div>Difference: <span id="difference" class="over">+1.6</span></div>
            </div>

            <div class="recommendation">
                <div class="recommendation-header">
                    <span>Betting Recommendation:</span>
                    <span class="value-badge">Value: <span id="valueRating">4</span>/10</span>
                </div>
                <div id="recommendationText" class="recommendation-text over">LEAN OVER 81.5 (1.9% edge)</div>
                <div id="edgeText" class="edge-text">Statistical edge: 1.9% over the line</div>
                <div id="confidenceText" class="edge-text" style="margin-top: 5px;">Confidence: <span id="confidenceRating">Medium</span></div>
                <div id="stayAwayText" style="display: none; margin-top: 5px; color: #991b1b; font-weight: bold;">⚠️ STAY AWAY - Low confidence prediction</div>
            </div>
        </div>
    </div>

    <script>
        // Toggle advanced mode
        document.getElementById('advancedMode').addEventListener('change', function() {
            const advancedFields = document.querySelectorAll('.advanced-field');
            const paceInfo = document.getElementById('paceInfo');
            const teamAHome = document.getElementById('teamAHome');
            const teamBHome = document.getElementById('teamBHome');

            if (this.checked) {
                advancedFields.forEach(field => field.style.display = 'block');
                paceInfo.style.display = 'block';
                teamAHome.disabled = false;
                teamBHome.disabled = false;
            } else {
                advancedFields.forEach(field => field.style.display = 'none');
                paceInfo.style.display = 'none';
                teamAHome.disabled = true;
                teamBHome.disabled = true;
            }

            calculatePrediction();
        });

        // Calculate prediction when inputs change
        const inputs = document.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('change', calculatePrediction);
            if (input.type === 'number') {
                input.addEventListener('input', calculatePrediction);
            }
        });

        // Home team toggle
        document.getElementById('teamAHome').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('teamBHome').checked = false;
            }
            calculatePrediction();
        });

        document.getElementById('teamBHome').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('teamAHome').checked = false;
            }
            calculatePrediction();
        });

        // Initial calculation
        calculatePrediction();

        function calculatePrediction() {
            // Get input values
            const advancedMode = document.getElementById('advancedMode').checked;

            // Get basic team stats
            const teamAOffense = parseFloat(document.getElementById('teamAOffense').value) || 0;
            const teamADefense = parseFloat(document.getElementById('teamADefense').value) || 0;
            const teamARecentOffense = parseFloat(document.getElementById('teamARecentOffense').value) || 0;

            const teamBOffense = parseFloat(document.getElementById('teamBOffense').value) || 0;
            const teamBDefense = parseFloat(document.getElementById('teamBDefense').value) || 0;
            const teamBRecentOffense = parseFloat(document.getElementById('teamBRecentOffense').value) || 0;

            // Calculate offensive variance automatically
            // Using a formula that estimates variance based on the difference between recent and season average
            const teamAOffVar = calculateOffensiveVariance(teamAOffense, teamARecentOffense);
            const teamBOffVar = calculateOffensiveVariance(teamBOffense, teamBRecentOffense);

            // Update the variance display fields
            document.getElementById('teamAOffVar').value = teamAOffVar.toFixed(1);
            document.getElementById('teamBOffVar').value = teamBOffVar.toFixed(1);

            const teamA = {
                offense: teamAOffense,
                defense: teamADefense,
                recentOffense: teamARecentOffense,
                offensiveVariance: teamAOffVar,
                homeAdvantage: document.getElementById('teamAHome').checked,
                restDays: parseInt(document.getElementById('teamARest').value) || 0
            };

            const teamB = {
                offense: teamBOffense,
                defense: teamBDefense,
                recentOffense: teamBRecentOffense,
                offensiveVariance: teamBOffVar,
                homeAdvantage: document.getElementById('teamBHome').checked,
                restDays: parseInt(document.getElementById('teamBRest').value) || 0
            };

            // H2H data
            const h2hData = {
                avgTotal: parseFloat(document.getElementById('h2hAvgTotal').value) || 0,
                trend: "neutral" // Default to neutral since we removed the trend selector
            };

            const bookieLine = parseFloat(document.getElementById('bookieLine').value) || 0;

            // Constants
            const LEAGUE_AVG_PACE = 97.5;
            const LEAGUE_AVG_PPG = 110.6;
            const LEAGUE_REGRESSION_WEIGHT = 0.2;

            // Calculate pace
            function calculateAdvancedPace(team) {
                const offensiveEfficiency = team.offense / LEAGUE_AVG_PPG;
                const defensiveEfficiency = team.defense / LEAGUE_AVG_PPG;
                const efficiencyPaceFactor = (offensiveEfficiency + defensiveEfficiency) / 2;

                const recentGamesWeight = 0.75;
                const recentFormRatio = team.recentOffense / team.offense;
                const bayesianRecentForm = (recentFormRatio * recentGamesWeight + 1 * (1 - recentGamesWeight));

                const regressionComponent = LEAGUE_REGRESSION_WEIGHT * LEAGUE_AVG_PACE;
                const teamComponent = (1 - LEAGUE_REGRESSION_WEIGHT) * (
                    LEAGUE_AVG_PACE * efficiencyPaceFactor * bayesianRecentForm
                );

                const calculatedPace = regressionComponent + teamComponent;
                const minPace = LEAGUE_AVG_PACE * 0.85;
                const maxPace = LEAGUE_AVG_PACE * 1.15;

                return Math.max(minPace, Math.min(maxPace, calculatedPace));
            }

            const teamAPace = calculateAdvancedPace(teamA);
            const teamBPace = calculateAdvancedPace(teamB);

            // Update pace displays
            document.getElementById('teamAPace').textContent = teamAPace.toFixed(1);
            document.getElementById('teamBPace').textContent = teamBPace.toFixed(1);
            document.getElementById('teamAPaceDisplay').value = teamAPace.toFixed(1);
            document.getElementById('teamBPaceDisplay').value = teamBPace.toFixed(1);

            // ===== ADVANCED STATISTICAL MODELS =====

            // 1. Basic model calculations
            const teamAPredictedScore = (teamA.offense + teamB.defense) / 2;
            const teamBPredictedScore = (teamB.offense + teamA.defense) / 2;
            const basicFullGameTotal = teamAPredictedScore + teamBPredictedScore;

            // 2. Recent form model (gives more weight to recent performance)
            const recentFormModel = advancedMode ?
                (teamA.recentOffense + teamB.defense) / 2 + (teamB.recentOffense + teamA.defense) / 2 :
                basicFullGameTotal;

            // 3. Pace-adjusted model
            const paceAdjustedModel = advancedMode ?
                basicFullGameTotal * ((teamAPace + teamBPace) / (2 * LEAGUE_AVG_PACE)) :
                basicFullGameTotal;

            // 4. Calculate pace effect for display
            const combinedPaceEffect = ((teamAPace + teamBPace) / 2 - LEAGUE_AVG_PACE) * 0.25;
            document.getElementById('paceImpact').textContent =
                (combinedPaceEffect > 0 ? "+" : "") + combinedPaceEffect.toFixed(1) + " pts";
            document.getElementById('paceImpact').className =
                combinedPaceEffect > 0 ? "over" : combinedPaceEffect < 0 ? "under" : "";

            // 5. Calculate offensive explosion potential
            const offensiveExplosionPotential = calculateOffensiveExplosionPotential(teamA, teamB);

            // 6. Calculate H2H adjustment
            const h2hAdjustment = calculateH2HAdjustment(h2hData, basicFullGameTotal);

            // 7. Detect hot streaks
            const teamAHotStreak = teamA.recentOffense > teamA.offense * 1.08;
            const teamBHotStreak = teamB.recentOffense > teamB.offense * 1.08;
            const hotStreakAdjustment = advancedMode ?
                (teamAHotStreak ? 2.5 : 0) + (teamBHotStreak ? 2.5 : 0) : 0;

            // 8. Non-linear transformation for team imbalance
            const teamAImbalance = Math.pow(Math.abs(teamA.offense - teamA.defense) / 10, 1.5);
            const teamBImbalance = Math.pow(Math.abs(teamB.offense - teamB.defense) / 10, 1.5);
            const imbalanceEffect = advancedMode ? (teamAImbalance + teamBImbalance) * 0.8 : 0;

            // Removed adaptive adjustment system as it requires post-game data

            // 10. ENSEMBLE MODEL - Combine all models with optimized weights
            let fullGameTotal = basicFullGameTotal;

            if (advancedMode) {
                // Enhanced ensemble model with all factors
                fullGameTotal = (basicFullGameTotal * 0.3) +
                               (recentFormModel * 0.5) +
                               (paceAdjustedModel * 0.2) +
                               offensiveExplosionPotential +
                               h2hAdjustment +
                               hotStreakAdjustment +
                               imbalanceEffect;
            }
            const firstHalfTotal = fullGameTotal / 2;

            // Update prediction displays
            document.getElementById('fullGameTotal').textContent = fullGameTotal.toFixed(1);
            document.getElementById('firstHalfTotal').textContent = firstHalfTotal.toFixed(1);
            document.getElementById('bookieLineDisplay').textContent = bookieLine.toFixed(1);

            // Calculate edge
            const difference = firstHalfTotal - bookieLine;
            document.getElementById('difference').textContent =
                (difference > 0 ? "+" : "") + difference.toFixed(1);
            document.getElementById('difference').className =
                difference > 0 ? "over" : "under";

            // Betting recommendation
            const edgePercentage = Math.abs(difference / bookieLine * 100).toFixed(1);
            let betStrength = "NO BET";

            if (Math.abs(difference) >= 2.5) {
                betStrength = "STRONG";
            } else if (Math.abs(difference) >= 1.5) {
                betStrength = "SOLID";
            } else if (Math.abs(difference) >= 0.75) {
                betStrength = "LEAN";
            }

            const betDirection = difference > 0 ? "OVER" : "UNDER";
            let totalSuggestion = "Line is fair, no clear edge";

            if (betStrength !== "NO BET") {
                totalSuggestion = `${betStrength} ${betDirection} ${bookieLine} (${edgePercentage}% edge)`;
            }

            document.getElementById('recommendationText').textContent = totalSuggestion;
            document.getElementById('recommendationText').className =
                "recommendation-text " + (betDirection.toLowerCase());

            document.getElementById('edgeText').textContent =
                betStrength !== "NO BET" ?
                `Statistical edge: ${edgePercentage}% over the line` :
                "No significant edge detected";

            // Calculate confidence rating
            const confidenceScore = calculateConfidenceScore(teamA, teamB, h2hData, difference, betStrength);
            let confidenceRating = "Low";
            if (confidenceScore >= 7) {
                confidenceRating = "High";
            } else if (confidenceScore >= 4) {
                confidenceRating = "Medium";
            }
            document.getElementById('confidenceRating').textContent = confidenceRating;

            // Show/hide stay away warning
            const stayAwayText = document.getElementById('stayAwayText');
            if (confidenceRating === "Low" && Math.abs(difference) < 1.5) {
                stayAwayText.style.display = "block";
            } else {
                stayAwayText.style.display = "none";
            }

            // Value rating - enhanced for over predictions
            let valueRating = Math.min(Math.round(Math.abs(difference) * 2), 10);

            // Boost value rating for over predictions with high confidence
            if (betDirection === "OVER" && confidenceRating === "High") {
                valueRating = Math.min(valueRating + 1, 10);
            }

            document.getElementById('valueRating').textContent = valueRating;
        }

        // Helper function to calculate offensive variance based on recent form
        function calculateOffensiveVariance(seasonAvg, recentAvg) {
            if (!seasonAvg || !recentAvg) {
                return 5.0; // Default value if data is missing
            }

            // Calculate variance based on the difference between recent and season average
            // Teams with bigger differences tend to have higher variance
            const difference = Math.abs(recentAvg - seasonAvg);

            // Base variance - all teams have some variance
            let variance = 3.0;

            // Add variance based on difference
            if (difference > 5) {
                variance += 4.0; // High variance
            } else if (difference > 3) {
                variance += 2.5; // Medium-high variance
            } else if (difference > 1) {
                variance += 1.0; // Medium variance
            }

            // Cap variance at reasonable limits
            return Math.min(Math.max(variance, 2.0), 8.0);
        }

        // Helper function to calculate offensive explosion potential
        function calculateOffensiveExplosionPotential(teamA, teamB) {
            // Check if variance data is available
            if (!teamA.offensiveVariance || !teamB.offensiveVariance) {
                return 0;
            }

            // Teams with high variance have more potential for explosive offensive games
            const combinedVariance = (teamA.offensiveVariance + teamB.offensiveVariance) / 2;

            // Calculate if teams are trending up offensively
            const teamATrend = teamA.recentOffense - teamA.offense;
            const teamBTrend = teamB.recentOffense - teamB.offense;
            const combinedTrend = teamATrend + teamBTrend;

            // Calculate explosion potential
            let explosionPotential = 0;

            // High variance teams
            if (combinedVariance > 6) {
                explosionPotential += 1.5;
            } else if (combinedVariance > 4) {
                explosionPotential += 0.8;
            }

            // Teams trending up offensively
            if (combinedTrend > 4) {
                explosionPotential += 1.2;
            } else if (combinedTrend > 2) {
                explosionPotential += 0.6;
            }

            return explosionPotential;
        }

        // Helper function to calculate H2H adjustment
        function calculateH2HAdjustment(h2hData, basicTotal) {
            if (!h2hData.avgTotal) {
                return 0;
            }

            // Calculate difference between H2H average and basic prediction
            const h2hDifference = h2hData.avgTotal - basicTotal;

            // Apply weight to H2H data (30%)
            let h2hAdjustment = h2hDifference * 0.3;

            // No trend adjustment since we removed the trend selector

            return h2hAdjustment;
        }

        // Helper function to calculate confidence score
        function calculateConfidenceScore(teamA, teamB, h2hData, difference, betStrength) {
            let score = 0;

            // Factor 1: Consistency of team offenses
            if (teamA.offensiveVariance && teamB.offensiveVariance) {
                const avgVariance = (teamA.offensiveVariance + teamB.offensiveVariance) / 2;
                if (avgVariance < 4) {
                    score += 2; // Low variance = more predictable
                } else if (avgVariance > 7) {
                    score -= 1; // High variance = less predictable
                }
            }

            // Factor 2: Recent form matches season averages
            const teamAFormDiff = Math.abs(teamA.recentOffense - teamA.offense);
            const teamBFormDiff = Math.abs(teamB.recentOffense - teamB.offense);
            if (teamAFormDiff < 2 && teamBFormDiff < 2) {
                score += 2; // Consistent performance
            } else if (teamAFormDiff > 5 || teamBFormDiff > 5) {
                score -= 1; // Inconsistent performance
            }

            // Factor 3: H2H data availability
            if (h2hData.avgTotal > 0) {
                score += 2; // H2H data available

                // Factor 4: H2H data consistency with prediction
                const h2hHalfTotal = h2hData.avgTotal / 2;
                const h2hDiff = Math.abs(h2hHalfTotal - (parseFloat(document.getElementById('firstHalfTotal').textContent)));
                if (h2hDiff < 3) {
                    score += 1; // H2H data supports prediction
                } else if (h2hDiff > 6) {
                    score -= 1; // H2H data contradicts prediction
                }
            }

            // Factor 5: Edge size
            if (Math.abs(difference) > 2.5) {
                score += 2; // Large edge
            } else if (Math.abs(difference) < 1) {
                score -= 1; // Small edge
            }

            // Factor 6: Bet strength
            if (betStrength === "STRONG") {
                score += 1;
            } else if (betStrength === "NO BET") {
                score -= 1;
            }

            return score;
        }

        // Removed adaptive learning system as it requires post-game data
    </script>
</body>
</html>
