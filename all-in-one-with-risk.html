<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basketball Calculator with Risk Assessment</title>
    <!-- Add React and ReactDOM directly -->
    <script crossorigin src="https://unpkg.com/react@17/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>

    <!-- Babel for JSX transpilation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        /* Basic styling for the calculator */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        #root {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Utility classes */
        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }
        .items-center { align-items: center; }
        .text-center { text-align: center; }
        .font-bold { font-weight: 700; }
        .text-2xl { font-size: 1.5rem; }
        .text-lg { font-size: 1.125rem; }
        .text-sm { font-size: 0.875rem; }
        .text-xs { font-size: 0.75rem; }
        .mb-1 { margin-bottom: 0.25rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-3 { margin-bottom: 0.75rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mt-1 { margin-top: 0.25rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-3 { margin-top: 0.75rem; }
        .mt-4 { margin-top: 1rem; }
        .ml-2 { margin-left: 0.5rem; }
        .ml-3 { margin-left: 0.75rem; }
        .p-2 { padding: 0.5rem; }
        .p-4 { padding: 1rem; }
        .rounded { border-radius: 0.25rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .border { border: 1px solid #e5e7eb; }
        .w-full { width: 100%; }
        .max-w-4xl { max-width: 56rem; }
        .mx-auto { margin-left: auto; margin-right: auto; }
        .grid { display: grid; }
        .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
        .gap-3 { gap: 0.75rem; }
        .gap-4 { gap: 1rem; }
        .block { display: block; }
        .inline-flex { display: inline-flex; }
        .cursor-pointer { cursor: pointer; }
        .font-medium { font-weight: 500; }
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border-width: 0;
        }

        /* Colors */
        .bg-gray-50 { background-color: #f9fafb; }
        .bg-gray-100 { background-color: #f3f4f6; }
        .bg-gray-200 { background-color: #e5e7eb; }
        .bg-blue-50 { background-color: #eff6ff; }
        .bg-blue-100 { background-color: #dbeafe; }
        .bg-blue-600 { background-color: #2563eb; }
        .bg-red-100 { background-color: #fee2e2; }
        .bg-purple-100 { background-color: #f3e8ff; }
        .border-blue-200 { border-color: #bfdbfe; }
        .text-white { color: white; }
        .text-gray-600 { color: #4b5563; }
        .text-gray-700 { color: #374151; }
        .text-gray-900 { color: #111827; }
        .text-blue-600 { color: #2563eb; }
        .text-blue-800 { color: #1e40af; }
        .text-red-600 { color: #dc2626; }
        .text-red-800 { color: #991b1b; }
        .text-purple-800 { color: #6b21a8; }
        .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }

        /* Form elements */
        input, select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.25rem;
            width: 100%;
        }

        input[readonly] {
            background-color: #f3f4f6;
        }

        /* Toggle switch */
        .toggle-container {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-checkbox {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-label {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-label:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .toggle-checkbox:checked + .toggle-label {
            background-color: #2563eb;
        }

        .toggle-checkbox:checked + .toggle-label:before {
            transform: translateX(26px);
        }

        /* Risk Assessment Styles */
        .risk-safe {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            border: 2px solid #16a34a;
        }
        .risk-caution {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 2px solid #ca8a04;
        }
        .risk-avoid {
            background: linear-gradient(135deg, #fecaca, #fca5a5);
            border: 2px solid #dc2626;
        }

        .text-green-600 { color: #16a34a; }
        .text-yellow-600 { color: #ca8a04; }
        .bg-green-600 { background-color: #16a34a; }
        .bg-yellow-600 { background-color: #ca8a04; }

        .confidence-bar {
            height: 8px;
            border-radius: 4px;
            background-color: #e5e7eb;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .alert-safe {
            background-color: #dcfce7;
            color: #166534;
            border-left: 4px solid #16a34a;
        }

        .alert-caution {
            background-color: #fef3c7;
            color: #92400e;
            border-left: 4px solid #ca8a04;
        }

        .alert-avoid {
            background-color: #fecaca;
            color: #991b1b;
            border-left: 4px solid #dc2626;
        }

        /* Media queries */
        @media (min-width: 768px) {
            .md\\:grid-cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <!-- Main component and rendering -->
    <script type="text/babel">
        // Render function
        function renderApp() {
          ReactDOM.render(
            <React.StrictMode>
              <EnhancedOverUnderCalculator />
            </React.StrictMode>,
            document.getElementById('root')
          );
        }

        // Main component - EXACT COPY from your all-in-one
        function EnhancedOverUnderCalculator() {
          const [teamA, setTeamA] = React.useState({
            name: "Team A",
            offense: 83.6,        // Season average PPG
            defense: 85.8,        // Season average points allowed
            recentOffense: 84.2,  // Last 10 games average points scored
            offensiveVariance: 5.2, // Standard deviation of points scored
            homeAdvantage: true,
            restDays: 2
          });

          const [teamB, setTeamB] = React.useState({
            name: "Team B",
            offense: 81.9,        // Season average PPG
            defense: 80.9,        // Season average points allowed
            recentOffense: 82.5,  // Last 10 games average points scored
            offensiveVariance: 4.8, // Standard deviation of points scored
            homeAdvantage: false,
            restDays: 1
          });

          const [bookieLine, setBookieLine] = React.useState(81.5);
          const [advancedMode, setAdvancedMode] = React.useState(false);
          const [h2hAvgTotal, setH2hAvgTotal] = React.useState("");
          const [confidenceRating, setConfidenceRating] = React.useState("Medium");

          // League average constants (based on historical NBA data)
          const LEAGUE_AVG_PACE = 97.5;
          const LEAGUE_AVG_PPG = 110.6;
          const LEAGUE_REGRESSION_WEIGHT = 0.2; // Weight for regression to the mean

          // Helper function to calculate offensive variance based on recent form
          const calculateOffensiveVariance = (seasonAvg, recentAvg) => {
            if (!seasonAvg || !recentAvg) {
                return 5.0; // Default value if data is missing
            }

            // Calculate variance based on the difference between recent and season average
            // Teams with bigger differences tend to have higher variance
            const difference = Math.abs(recentAvg - seasonAvg);

            // Base variance - all teams have some variance
            let variance = 3.0;

            // Add variance based on difference
            if (difference > 5) {
                variance += 4.0; // High variance
            } else if (difference > 3) {
                variance += 2.5; // Medium-high variance
            } else if (difference > 1) {
                variance += 1.0; // Medium variance
            }

            // Cap variance at reasonable limits
            return Math.min(Math.max(variance, 2.0), 8.0);
          };

          // Function to calculate pace using advanced statistical methods
          const calculateAdvancedPace = (team) => {
            // Base calculation using offensive and defensive efficiency
            const offensiveEfficiency = team.offense / LEAGUE_AVG_PPG;
            const defensiveEfficiency = team.defense / LEAGUE_AVG_PPG;
            const efficiencyPaceFactor = (offensiveEfficiency + defensiveEfficiency) / 2;

            // Recent form adjustment with Bayesian update
            const recentGamesWeight = 0.75; // Assuming we have ~10 games of recent data
            const recentFormRatio = team.recentOffense / team.offense;
            const bayesianRecentForm = (recentFormRatio * recentGamesWeight + 1 * (1 - recentGamesWeight));

            // Regression to the mean component
            const regressionComponent = LEAGUE_REGRESSION_WEIGHT * LEAGUE_AVG_PACE;

            // Team-specific component with higher weight
            const teamComponent = (1 - LEAGUE_REGRESSION_WEIGHT) * (
              LEAGUE_AVG_PACE * efficiencyPaceFactor * bayesianRecentForm
            );

            // Final pace calculation with confidence adjustment
            const calculatedPace = regressionComponent + teamComponent;

            // Apply constraints to keep pace within reasonable bounds
            const minPace = LEAGUE_AVG_PACE * 0.85; // Slowest teams are ~15% below average
            const maxPace = LEAGUE_AVG_PACE * 1.15; // Fastest teams are ~15% above average

            return Math.max(minPace, Math.min(maxPace, calculatedPace));
          };

          // Calculate pace values
          const teamAPace = calculateAdvancedPace(teamA);
          const teamBPace = calculateAdvancedPace(teamB);

          // ORIGINAL MODEL CALCULATIONS
          // Step 1: Explosiveness Index (EI)
          const explosiveIndexA = teamA.offense - teamB.defense;
          const explosiveIndexB = teamB.offense - teamA.defense;
          const explosiveIndex = explosiveIndexA + explosiveIndexB;

          let eiSuggestion = "Balanced (could go either way)";
          if (explosiveIndex >= 4) {
            eiSuggestion = "High chance of OVER";
          } else if (explosiveIndex <= -2) {
            eiSuggestion = "Low-scoring game, good for UNDER";
          }

          // Step 2: Scoring Consistency Index (SCI)
          const teamAConsistency = Math.abs(teamA.offense - teamA.defense);
          const teamBConsistency = Math.abs(teamB.offense - teamB.defense);
          const scoringConsistencyIndex = (teamAConsistency + teamBConsistency) / 2;

          let sciSuggestion = "Average, can go either way";
          if (scoringConsistencyIndex <= 2) {
            sciSuggestion = "Teams are stable → likely UNDER";
          } else if (scoringConsistencyIndex >= 4) {
            sciSuggestion = "Very unstable → can swing to OVER";
          }

          // Step 3: Predicted Score (Original)
          const teamAPredictedScore = (teamA.offense + teamB.defense) / 2;
          const teamBPredictedScore = (teamB.offense + teamA.defense) / 2;
          const basicFullGameTotal = teamAPredictedScore + teamBPredictedScore;

          // ===== ADVANCED STATISTICAL MODELS =====

          // 1. Basic model calculations (already done above)

          // 2. Recent form model (gives more weight to recent performance)
          const recentFormModel = (teamA.recentOffense + teamB.defense) / 2 + (teamB.recentOffense + teamA.defense) / 2;

          // 3. Pace-adjusted model
          const paceAdjustedModel = basicFullGameTotal * ((teamAPace + teamBPace) / (2 * LEAGUE_AVG_PACE));

          // 4. Calculate pace effect for display
          const combinedPaceEffect = ((teamAPace + teamBPace) / 2 - LEAGUE_AVG_PACE) * 0.25;

          // 5. RECENT FORM ANALYSIS (for component calculations)
          const recentFormWeight = 0.7;
          const teamAOffenseAdjusted = teamA.recentOffense * recentFormWeight + teamA.offense * (1 - recentFormWeight);
          const teamADefenseAdjusted = teamA.defense;
          const teamBOffenseAdjusted = teamB.recentOffense * recentFormWeight + teamB.offense * (1 - recentFormWeight);
          const teamBDefenseAdjusted = teamB.defense;

          // 6. OFFENSIVE EXPLOSION POTENTIAL
          let offensiveExplosionPotential = 0;
          const teamAVariance = calculateOffensiveVariance(teamA.offense, teamA.recentOffense);
          const teamBVariance = calculateOffensiveVariance(teamB.offense, teamB.recentOffense);

          // Teams with high variance have more potential for explosive offensive games
          const combinedVariance = (teamAVariance + teamBVariance) / 2;

          // Calculate if teams are trending up offensively
          const teamATrend = teamA.recentOffense - teamA.offense;
          const teamBTrend = teamB.recentOffense - teamB.offense;
          const combinedTrend = teamATrend + teamBTrend;

          // High variance teams
          if (combinedVariance > 6) {
            offensiveExplosionPotential += 1.5;
          } else if (combinedVariance > 4) {
            offensiveExplosionPotential += 0.8;
          }

          // Teams trending up offensively
          if (combinedTrend > 4) {
            offensiveExplosionPotential += 1.2;
          } else if (combinedTrend > 2) {
            offensiveExplosionPotential += 0.6;
          }

          // 7. H2H ADJUSTMENT
          let h2hAdjustment = 0;
          if (h2hAvgTotal) {
            // Calculate difference between H2H average and basic prediction
            const h2hDifference = h2hAvgTotal - basicFullGameTotal;

            // Apply weight to H2H data (30%)
            h2hAdjustment = h2hDifference * 0.3;
          }

          // 8. Detect hot streaks
          const teamAHotStreak = teamA.recentOffense > teamA.offense * 1.08;
          const teamBHotStreak = teamB.recentOffense > teamB.offense * 1.08;
          const hotStreakAdjustment = (teamAHotStreak ? 2.5 : 0) + (teamBHotStreak ? 2.5 : 0);

          // 9. Non-linear transformation for team imbalance
          const teamAImbalance = Math.pow(Math.abs(teamA.offense - teamA.defense) / 10, 1.5);
          const teamBImbalance = Math.pow(Math.abs(teamB.offense - teamB.defense) / 10, 1.5);
          const imbalanceEffect = (teamAImbalance + teamBImbalance) * 0.8;

          // 3. HOME COURT ADVANTAGE
          const baseHomeAdvantage = 2.5;
          const homeOffensiveBoost = 0.2;
          const homeAdvantagePoints = baseHomeAdvantage +
            (teamA.homeAdvantage ? (teamA.offense > LEAGUE_AVG_PPG * 0.8 ? homeOffensiveBoost : 0) :
             teamB.homeAdvantage ? (teamB.offense > LEAGUE_AVG_PPG * 0.8 ? homeOffensiveBoost : 0) : 0);

          const homeAwayAdjustment = teamA.homeAdvantage ? homeAdvantagePoints :
                                     teamB.homeAdvantage ? -homeAdvantagePoints : 0;

          // 4. REST ADVANTAGE ANALYSIS
          const getRestFactor = (days) => {
            if (days === 0) return -2.0;
            if (days === 1) return -0.7;
            if (days >= 3) return 1.2;
            return 0;
          };

          const restAdjustment = getRestFactor(teamA.restDays) + getRestFactor(teamB.restDays);

          // 5. MATCHUP-SPECIFIC ADJUSTMENTS
          const styleSimilarity = 1 - (Math.abs(teamA.offense - teamB.offense) /
                                   Math.max(teamA.offense, teamB.offense));

          const defenseDominance = (teamA.defense < teamB.offense * 0.9) ||
                                  (teamB.defense < teamA.offense * 0.9) ? -1.5 : 0;

          // 6. SCORING EFFICIENCY ADJUSTMENT
          const teamAEfficiency = teamA.offense / teamAPace;
          const teamBEfficiency = teamB.offense / teamBPace;
          const efficiencyAdjustment = ((teamAEfficiency + teamBEfficiency) / 2) * 2.0;

          // ===== ENSEMBLE MODEL CALCULATION =====
          let fullGameTotal;

          if (advancedMode) {
            // Advanced ensemble model with all adjustments
            const ensembleTotal = (basicFullGameTotal * 0.3) + (recentFormModel * 0.5) + (paceAdjustedModel * 0.2);

            fullGameTotal = ensembleTotal +
              offensiveExplosionPotential +
              h2hAdjustment +
              hotStreakAdjustment +
              imbalanceEffect +
              homeAwayAdjustment +
              restAdjustment +
              defenseDominance +
              efficiencyAdjustment;
          } else {
            // Basic model
            fullGameTotal = basicFullGameTotal;
          }

          const firstHalfTotal = fullGameTotal / 2;

          // ===== BETTING RECOMMENDATION SYSTEM =====
          const difference = firstHalfTotal - bookieLine;
          const edgePercentage = Math.abs(difference / bookieLine * 100).toFixed(1);

          let betStrength = "NO BET";
          let betConfidence = 0;

          if (Math.abs(difference) >= 2.5) {
            betStrength = "STRONG";
            betConfidence = 85;
          } else if (Math.abs(difference) >= 1.5) {
            betStrength = "MEDIUM";
            betConfidence = 70;
          } else if (Math.abs(difference) >= 0.8) {
            betStrength = "WEAK";
            betConfidence = 55;
          }

          const totalSuggestion = difference > 0 ? `OVER ${bookieLine}` : difference < 0 ? `UNDER ${bookieLine}` : "NO BET";

          // ===== CONFIDENCE SCORING SYSTEM =====
          let confidenceScore = 0;

          // Factor 1: Variance analysis
          const avgVariance = (teamAVariance + teamBVariance) / 2;
          if (avgVariance < 4) {
            confidenceScore += 2;
          } else if (avgVariance > 7) {
            confidenceScore -= 1;
          }

          // Factor 2: Recent form consistency
          const teamAFormDiff = Math.abs(teamA.recentOffense - teamA.offense);
          const teamBFormDiff = Math.abs(teamB.recentOffense - teamB.offense);
          if (teamAFormDiff < 2 && teamBFormDiff < 2) {
            confidenceScore += 2;
          } else if (teamAFormDiff > 5 || teamBFormDiff > 5) {
            confidenceScore -= 1;
          }

          // Factor 3: H2H data availability
          if (h2hAvgTotal > 0) {
            confidenceScore += 2;
          }

          // Factor 4: Edge size
          if (Math.abs(difference) > 2) {
            confidenceScore += 2;
          } else if (Math.abs(difference) < 1) {
            confidenceScore -= 1;
          }

          // Factor 5: Model consensus
          const predictions = [basicFullGameTotal/2, recentFormModel/2, paceAdjustedModel/2];
          const avgPrediction = predictions.reduce((a, b) => a + b, 0) / predictions.length;
          const maxDeviation = Math.max(...predictions.map(p => Math.abs(p - avgPrediction)));
          if (maxDeviation < 2) {
            confidenceScore += 2;
          } else if (maxDeviation > 4) {
            confidenceScore -= 1;
          }

          // Set confidence rating based on score
          let newConfidenceRating = "Low";
          if (confidenceScore >= 7) {
            newConfidenceRating = "High";
          } else if (confidenceScore >= 4) {
            newConfidenceRating = "Medium";
          }

          // Update confidence rating state
          if (newConfidenceRating !== confidenceRating) {
            setConfidenceRating(newConfidenceRating);
          }

          // Value Rating (1-10 scale)
          let valueRating = 0;

          if (advancedMode) {
            // Calculate value rating based on edge and confidence
            const edgeValue = Math.min(Math.abs(difference) * 2, 5); // Max 5 points from edge
            const confidenceValue = Math.min(confidenceScore * 0.5, 5); // Max 5 points from confidence
            valueRating = Math.round(edgeValue + confidenceValue);

            // Boost value rating for over predictions with high confidence
            if (difference > 0 && newConfidenceRating === "High") {
              valueRating = Math.min(valueRating + 1, 10);
            }

            // Cap at 10
            valueRating = Math.min(valueRating, 10);
          } else {
            // Simpler rating for basic mode
            valueRating = Math.min(Math.abs(difference) * 3, 10);
          }

          // ===== RISK ASSESSMENT FOR 7-10 GAME TICKETS =====

          // 1. Model Consensus Risk (How much do predictions agree?)
          let consensusRisk = 0;
          if (maxDeviation > 4) consensusRisk = 8; // High risk
          else if (maxDeviation > 2) consensusRisk = 5; // Medium risk
          else consensusRisk = 2; // Low risk

          // 2. Variance Risk (High variance = unpredictable)
          let varianceRisk = 0;
          if (avgVariance > 6.5) varianceRisk = 8;
          else if (avgVariance > 5) varianceRisk = 5;
          else varianceRisk = 2;

          // 3. Form Inconsistency Risk
          const maxFormDiff = Math.max(teamAFormDiff, teamBFormDiff);
          let formRisk = 0;
          if (maxFormDiff > 6) formRisk = 8;
          else if (maxFormDiff > 4) formRisk = 5;
          else formRisk = 2;

          // 4. H2H Contradiction Risk
          let h2hRisk = 3; // Default medium risk when no H2H data
          if (h2hAvgTotal > 0) {
            const h2hHalfTotal = h2hAvgTotal / 2;
            const h2hDiff = Math.abs(h2hHalfTotal - firstHalfTotal);
            if (h2hDiff > 6) h2hRisk = 8;
            else if (h2hDiff > 3) h2hRisk = 5;
            else h2hRisk = 1;
          }

          // 5. Edge Size Risk (Small edges are risky)
          let edgeRisk = 0;
          const edgeSize = Math.abs(difference);
          if (edgeSize < 1) edgeRisk = 7;
          else if (edgeSize < 2) edgeRisk = 4;
          else edgeRisk = 1;

          // 6. Rest Disparity Risk
          const restDiff = Math.abs(teamA.restDays - teamB.restDays);
          let restRisk = 0;
          if (restDiff >= 2) restRisk = 6;
          else if (restDiff === 1) restRisk = 3;
          else restRisk = 1;

          // OVERALL RISK CALCULATION (weighted for your betting pattern)
          const riskFactors = [
            { name: "Model Consensus", risk: consensusRisk, weight: 0.25 },
            { name: "Team Variance", risk: varianceRisk, weight: 0.20 },
            { name: "Form Consistency", risk: formRisk, weight: 0.20 },
            { name: "H2H Contradiction", risk: h2hRisk, weight: 0.15 },
            { name: "Edge Size", risk: edgeRisk, weight: 0.15 },
            { name: "Rest Disparity", risk: restRisk, weight: 0.05 }
          ];

          const overallRisk = riskFactors.reduce((total, factor) =>
            total + (factor.risk * factor.weight), 0);

          // RISK CLASSIFICATION FOR 7-10 GAME TICKETS
          let riskLevel, riskClass, recommendation, alertClass;

          if (overallRisk <= 3) {
            riskLevel = "SAFE TO BET";
            riskClass = "risk-safe";
            recommendation = "✅ INCLUDE IN TICKET - Low risk game";
            alertClass = "alert-safe";
          } else if (overallRisk <= 5.5) {
            riskLevel = "USE WITH CAUTION";
            riskClass = "risk-caution";
            recommendation = "⚠️ LIMIT TO 2-3 PER TICKET - Medium risk";
            alertClass = "alert-caution";
          } else {
            riskLevel = "AVOID COMPLETELY";
            riskClass = "risk-avoid";
            recommendation = "❌ DO NOT BET - High risk of loss";
            alertClass = "alert-avoid";
          }

          // Input handlers
          const handleTeamAChange = (field, value) => {
            setTeamA({...teamA, [field]: typeof value === 'boolean' ? value : parseFloat(value) || 0});
          };

          const handleTeamBChange = (field, value) => {
            setTeamB({...teamB, [field]: typeof value === 'boolean' ? value : parseFloat(value) || 0});
          };

          const handleBookieLineChange = (e) => {
            setBookieLine(parseFloat(e.target.value) || 0);
          };

          // Render the UI
          return (
            <div className="flex flex-col p-4 max-w-4xl mx-auto bg-gray-50 rounded-lg shadow-md">
              <h1 className="text-2xl font-bold text-center mb-4">Basketball Calculator with Risk Assessment</h1>

              {/* Risk Assessment Alert */}
              <div className={`p-4 rounded-lg shadow-md mb-4 ${riskClass}`}>
                <div className="text-center">
                  <h2 className="text-xl font-bold mb-2">🎯 RISK ASSESSMENT</h2>
                  <div className="text-2xl font-bold mb-2">{riskLevel}</div>
                  <div className="confidence-bar mb-2">
                    <div
                      className={`confidence-fill ${
                        overallRisk <= 3 ? 'bg-green-600' :
                        overallRisk <= 5.5 ? 'bg-yellow-600' : 'bg-red-600'
                      }`}
                      style={{width: `${Math.min(overallRisk * 12, 100)}%`}}
                    ></div>
                  </div>
                  <div className="text-lg">Risk Score: {overallRisk.toFixed(1)}/10</div>
                  <div className="mt-2 font-medium">{recommendation}</div>
                </div>
              </div>

              <div className="mb-4 flex justify-center">
                <label className="inline-flex items-center cursor-pointer">
                  <div className="toggle-container">
                    <input
                      type="checkbox"
                      checked={advancedMode}
                      onChange={() => setAdvancedMode(!advancedMode)}
                      className="toggle-checkbox"
                    />
                    <span className="toggle-label"></span>
                  </div>
                  <span className="ml-3 text-sm font-medium text-gray-900">Use Enhanced Model (Optimized for OVER)</span>
                </label>
              </div>

              {advancedMode && (
                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h2 className="text-lg font-bold text-blue-800">Pace Factor Estimation</h2>
                  <div className="mt-3">
                    <p className="text-sm text-blue-800">
                      Pace is automatically calculated based on team offense, defense, and recent scoring.
                    </p>
                  </div>
                  <div className="mt-4 text-sm">
                    <div className="font-medium">Calculated Pace Values:</div>
                    <div className="flex justify-between mt-1">
                      <div>Team A: <span className="font-bold">{teamAPace.toFixed(1)}</span></div>
                      <div>Team B: <span className="font-bold">{teamBPace.toFixed(1)}</span></div>
                      <div>League Avg: <span className="font-bold">{LEAGUE_AVG_PACE}</span></div>
                      <div>Impact: <span className={`font-bold ${combinedPaceEffect > 0 ? "text-red-600" : combinedPaceEffect < 0 ? "text-blue-600" : ""}`}>
                        {combinedPaceEffect > 0 ? "+" : ""}{combinedPaceEffect.toFixed(1)} pts
                      </span></div>
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                {/* Team A Stats */}
                <div className="p-4 bg-blue-100 rounded-lg">
                  <div className="flex justify-between items-center mb-3">
                    <h2 className="text-lg font-bold text-blue-800">Team A Stats</h2>
                    <div>
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={teamA.homeAdvantage}
                          onChange={(e) => {
                            handleTeamAChange('homeAdvantage', e.target.checked);
                            if (e.target.checked) handleTeamBChange('homeAdvantage', false);
                          }}
                          disabled={!advancedMode}
                        />
                        <span className="ml-2 text-sm text-gray-700">Home Team</span>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="mb-2">
                      <label className="block text-sm font-medium mb-1">Season Offense (PPG):</label>
                      <input
                        type="number"
                        value={teamA.offense}
                        onChange={(e) => handleTeamAChange('offense', e.target.value)}
                        className="w-full p-2 border rounded"
                        step="0.1"
                      />
                    </div>
                    <div className="mb-2">
                      <label className="block text-sm font-medium mb-1">Season Defense (PPG Allowed):</label>
                      <input
                        type="number"
                        value={teamA.defense}
                        onChange={(e) => handleTeamAChange('defense', e.target.value)}
                        className="w-full p-2 border rounded"
                        step="0.1"
                      />
                    </div>

                    {advancedMode && (
                      <>
                        <div className="mb-2">
                          <label className="block text-sm font-medium mb-1">Last 10 Games Points Scored:</label>
                          <input
                            type="number"
                            value={teamA.recentOffense}
                            onChange={(e) => handleTeamAChange('recentOffense', e.target.value)}
                            className="w-full p-2 border rounded"
                            step="0.1"
                          />
                        </div>
                        <div className="mb-2">
                          <label className="block text-sm font-medium mb-1">Offensive Variance (auto-calculated):</label>
                          <input
                            type="number"
                            value={calculateOffensiveVariance(teamA.offense, teamA.recentOffense).toFixed(1)}
                            readOnly
                            className="w-full p-2 border rounded bg-gray-100"
                            title="Standard deviation of points scored - calculated based on recent form"
                          />
                        </div>
                        <div className="mb-2">
                          <label className="block text-sm font-medium mb-1">Pace Factor (Auto-calculated):</label>
                          <input
                            type="number"
                            value={teamAPace.toFixed(1)}
                            readOnly
                            className="w-full p-2 border rounded bg-gray-100"
                          />
                        </div>
                        <div className="mb-2">
                          <label className="block text-sm font-medium mb-1">Days of Rest:</label>
                          <select
                            value={teamA.restDays}
                            onChange={(e) => handleTeamAChange('restDays', e.target.value)}
                            className="w-full p-2 border rounded"
                          >
                            <option value={0}>0 (Back-to-Back)</option>
                            <option value={1}>1 Day</option>
                            <option value={2}>2 Days</option>
                            <option value={3}>3+ Days</option>
                          </select>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Team B Stats */}
                <div className="p-4 bg-red-100 rounded-lg">
                  <div className="flex justify-between items-center mb-3">
                    <h2 className="text-lg font-bold text-red-800">Team B Stats</h2>
                    <div>
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={teamB.homeAdvantage}
                          onChange={(e) => {
                            handleTeamBChange('homeAdvantage', e.target.checked);
                            if (e.target.checked) handleTeamAChange('homeAdvantage', false);
                          }}
                          disabled={!advancedMode}
                        />
                        <span className="ml-2 text-sm text-gray-700">Home Team</span>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="mb-2">
                      <label className="block text-sm font-medium mb-1">Season Offense (PPG):</label>
                      <input
                        type="number"
                        value={teamB.offense}
                        onChange={(e) => handleTeamBChange('offense', e.target.value)}
                        className="w-full p-2 border rounded"
                        step="0.1"
                      />
                    </div>
                    <div className="mb-2">
                      <label className="block text-sm font-medium mb-1">Season Defense (PPG Allowed):</label>
                      <input
                        type="number"
                        value={teamB.defense}
                        onChange={(e) => handleTeamBChange('defense', e.target.value)}
                        className="w-full p-2 border rounded"
                        step="0.1"
                      />
                    </div>

                    {advancedMode && (
                      <>
                        <div className="mb-2">
                          <label className="block text-sm font-medium mb-1">Last 10 Games Points Scored:</label>
                          <input
                            type="number"
                            value={teamB.recentOffense}
                            onChange={(e) => handleTeamBChange('recentOffense', e.target.value)}
                            className="w-full p-2 border rounded"
                            step="0.1"
                          />
                        </div>
                        <div className="mb-2">
                          <label className="block text-sm font-medium mb-1">Offensive Variance (auto-calculated):</label>
                          <input
                            type="number"
                            value={calculateOffensiveVariance(teamB.offense, teamB.recentOffense).toFixed(1)}
                            readOnly
                            className="w-full p-2 border rounded bg-gray-100"
                            title="Standard deviation of points scored - calculated based on recent form"
                          />
                        </div>
                        <div className="mb-2">
                          <label className="block text-sm font-medium mb-1">Pace Factor (Auto-calculated):</label>
                          <input
                            type="number"
                            value={teamBPace.toFixed(1)}
                            readOnly
                            className="w-full p-2 border rounded bg-gray-100"
                          />
                        </div>
                        <div className="mb-2">
                          <label className="block text-sm font-medium mb-1">Days of Rest:</label>
                          <select
                            value={teamB.restDays}
                            onChange={(e) => handleTeamBChange('restDays', e.target.value)}
                            className="w-full p-2 border rounded"
                          >
                            <option value={0}>0 (Back-to-Back)</option>
                            <option value={1}>1 Day</option>
                            <option value={2}>2 Days</option>
                            <option value={3}>3+ Days</option>
                          </select>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {advancedMode && (
                <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h2 className="text-lg font-bold text-blue-800 mb-2">Head-to-Head History (Optional)</h2>
                  <p className="text-sm text-blue-800 mb-3">Only fill this if you have H2H data available</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="mb-2">
                      <label className="block text-sm font-medium mb-1">Average Total Points (Last 10 H2H Games):</label>
                      <input
                        type="number"
                        value={h2hAvgTotal || ""}
                        onChange={(e) => setH2hAvgTotal(parseFloat(e.target.value) || 0)}
                        className="w-full p-2 border rounded"
                        placeholder="Leave empty if unknown"
                        step="0.1"
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="mb-4 p-4 bg-purple-100 rounded-lg">
                <label className="block text-sm font-medium mb-1 text-purple-800">Bookie 1st Half Total Line:</label>
                <input
                  type="number"
                  value={bookieLine}
                  onChange={handleBookieLineChange}
                  className="w-full p-2 border rounded"
                  step="0.5"
                />
              </div>

              {/* Risk Factor Breakdown */}
              {advancedMode && (
                <div className="mb-4 p-4 bg-gray-100 rounded-lg">
                  <h3 className="text-lg font-bold mb-3">📊 Risk Factor Breakdown</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {riskFactors.map((factor, index) => {
                      const riskColor = factor.risk <= 3 ? 'text-green-600' :
                                      factor.risk <= 5 ? 'text-yellow-600' : 'text-red-600';
                      const bgColor = factor.risk <= 3 ? 'bg-green-600' :
                                    factor.risk <= 5 ? 'bg-yellow-600' : 'bg-red-600';

                      return (
                        <div key={index} className="border rounded-lg p-3 bg-white">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-sm">{factor.name}</span>
                            <span className={`font-bold ${riskColor}`}>
                              {factor.risk.toFixed(1)}/10
                            </span>
                          </div>
                          <div className="confidence-bar mb-1">
                            <div
                              className={`confidence-fill ${bgColor}`}
                              style={{width: `${factor.risk * 10}%`}}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-600">
                            Weight: {(factor.weight * 100).toFixed(0)}%
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <EnhancedResults
                  fullGameTotal={fullGameTotal}
                  firstHalfTotal={firstHalfTotal}
                  bookieLine={bookieLine}
                  difference={difference}
                  totalSuggestion={totalSuggestion}
                  edgePercentage={edgePercentage}
                  betStrength={betStrength}
                  valueRating={valueRating}
                  confidenceRating={newConfidenceRating}
                />
              </div>
            </div>
          );
        }

        // Enhanced Results component with feedback section
        function EnhancedResults({ fullGameTotal, firstHalfTotal, bookieLine, difference, totalSuggestion, edgePercentage, betStrength, valueRating, confidenceRating }) {
          return (
            <div className="p-4 bg-gray-100 rounded-lg">
              <h3 className="font-bold mb-2">Prediction Results</h3>
              <div className="text-sm mb-2">
                <div>Full Game Total: <span className="font-bold">{fullGameTotal.toFixed(1)}</span></div>
                <div>First Half Total: <span className="font-bold">{firstHalfTotal.toFixed(1)}</span></div>
                <div>Bookie Line: <span className="font-bold">{bookieLine}</span></div>
                <div>Difference: <span className={`font-bold ${difference > 0 ? "text-red-600" : "text-blue-600"}`}>
                  {difference > 0 ? "+" : ""}{difference.toFixed(1)}
                </span></div>
              </div>

              <div className="mt-4 p-3 bg-gray-100 rounded">
                <div className="font-bold flex justify-between items-center">
                  <span>Betting Recommendation:</span>
                  <span className="text-sm bg-blue-600 text-white px-2 py-1 rounded">
                    Value: {valueRating}/10
                  </span>
                </div>
                <div className={`text-lg font-bold ${totalSuggestion.includes("OVER") ? "text-red-600" : totalSuggestion.includes("UNDER") ? "text-blue-600" : ""}`}>
                  {totalSuggestion}
                </div>
                <div className="text-xs mt-1 text-gray-600">
                  {betStrength !== "NO BET"
                    ? `Statistical edge: ${edgePercentage}% over the line`
                    : "No significant edge detected"}
                </div>
                <div className="text-xs mt-1 text-gray-600">
                  Confidence: <span className="font-medium">{confidenceRating}</span>
                </div>
                {confidenceRating === "Low" && Math.abs(difference) < 1.5 && (
                  <div className="mt-2 text-sm font-bold text-red-600">
                    ⚠️ STAY AWAY - Low confidence prediction
                  </div>
                )}
              </div>
            </div>
          );
        }

        // Call the render function
        renderApp();