Stack trace:
Frame         Function      Args
0007FFFF92C0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF92C0, 0007FFFF81C0) msys-2.0.dll+0x2118E
0007FFFF92C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF92C0  0002100469F2 (00021028DF99, 0007FFFF9178, 0007FFFF92C0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF92C0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF92C0  00021006A545 (0007FFFF92D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF92D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF89CA40000 ntdll.dll
7FF89AB00000 KERNEL32.DLL
7FF899A00000 KERNELBASE.dll
7FF89BF20000 USER32.dll
7FF8999D0000 win32u.dll
7FF89B8D0000 GDI32.dll
7FF89A960000 gdi32full.dll
7FF899CB0000 msvcp_win.dll
7FF89A620000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF89C280000 advapi32.dll
7FF89AD60000 msvcrt.dll
7FF89C390000 sechost.dll
7FF89BBF0000 RPCRT4.dll
7FF8992B0000 CRYPTBASE.DLL
7FF89A7C0000 bcryptPrimitives.dll
7FF89BBB0000 IMM32.DLL
