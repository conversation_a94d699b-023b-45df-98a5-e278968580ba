import React, { useState } from "react";

export default function EnhancedOverUnderCalculator() {
  const [teamA, setTeamA] = useState({
    name: "Team A",
    offense: 83.6,        // Season average PPG
    defense: 85.8,        // Season average points allowed
    recentOffense: 84.2,  // Last 10 games average points scored
    homeAdvantage: true,
    restDays: 2
  });

  const [teamB, setTeamB] = useState({
    name: "Team B",
    offense: 81.9,        // Season average PPG
    defense: 80.9,        // Season average points allowed
    recentOffense: 82.5,  // Last 10 games average points scored
    homeAdvantage: false,
    restDays: 1
  });

  const [bookieLine, setBookieLine] = useState(81.5);
  const [advancedMode, setAdvancedMode] = useState(false);
  // No need for advanced pace inputs since we don't have the advanced stats

  // Advanced Statistical Model for Pace Estimation
  // Using multiple regression techniques and Bayesian adjustments

  // League average constants (based on historical NBA data)
  const LEAGUE_AVG_PACE = 97.5;
  const LEAGUE_AVG_PPG = 110.6;
  const LEAGUE_AVG_POSSESSIONS = 100;
  const LEAGUE_REGRESSION_WEIGHT = 0.2; // Weight for regression to the mean

  // Function to calculate pace using advanced statistical methods
  const calculateAdvancedPace = (team) => {
    // 1. Base calculation using offensive and defensive efficiency
    const offensiveEfficiency = team.offense / LEAGUE_AVG_PPG;
    const defensiveEfficiency = team.defense / LEAGUE_AVG_PPG;

    // Teams that score more and allow more points tend to play at a faster pace
    const efficiencyPaceFactor = (offensiveEfficiency + defensiveEfficiency) / 2;

    // 2. Recent form adjustment with Bayesian update
    // Calculate the weight to give to recent performance (more games = more weight)
    const recentGamesWeight = 0.75; // Assuming we have ~10 games of recent data

    // Calculate recent form factor with Bayesian adjustment
    const recentFormRatio = team.recentOffense / team.offense;
    const bayesianRecentForm = (recentFormRatio * recentGamesWeight + 1 * (1 - recentGamesWeight));

    // 3. Regression to the mean component
    // Teams with extreme pace tend to regress toward league average
    const regressionComponent = LEAGUE_REGRESSION_WEIGHT * LEAGUE_AVG_PACE;

    // 4. Team-specific component with higher weight
    const teamComponent = (1 - LEAGUE_REGRESSION_WEIGHT) * (
      LEAGUE_AVG_PACE * efficiencyPaceFactor * bayesianRecentForm
    );

    // 5. Final pace calculation with confidence adjustment
    const calculatedPace = regressionComponent + teamComponent;

    // 6. Apply constraints to keep pace within reasonable bounds
    const minPace = LEAGUE_AVG_PACE * 0.85; // Slowest teams are ~15% below average
    const maxPace = LEAGUE_AVG_PACE * 1.15; // Fastest teams are ~15% above average

    return Math.max(minPace, Math.min(maxPace, calculatedPace));
  };

  // Calculate pace values with confidence intervals
  const teamAPace = calculateAdvancedPace(teamA);
  const teamBPace = calculateAdvancedPace(teamB);

  // Calculate confidence intervals (±5% range)
  const teamAPaceMin = teamAPace * 0.95;
  const teamAPaceMax = teamAPace * 1.05;
  const teamBPaceMin = teamBPace * 0.95;
  const teamBPaceMax = teamBPace * 1.05;

  // ORIGINAL MODEL CALCULATIONS
  // Step 1: Explosiveness Index (EI)
  const explosiveIndexA = teamA.offense - teamB.defense;
  const explosiveIndexB = teamB.offense - teamA.defense;
  const explosiveIndex = explosiveIndexA + explosiveIndexB;

  let eiSuggestion = "Balanced (could go either way)";
  if (explosiveIndex >= 4) {
    eiSuggestion = "High chance of OVER";
  } else if (explosiveIndex <= -2) {
    eiSuggestion = "Low-scoring game, good for UNDER";
  }

  // Step 2: Scoring Consistency Index (SCI)
  const teamAConsistency = Math.abs(teamA.offense - teamA.defense);
  const teamBConsistency = Math.abs(teamB.offense - teamB.defense);
  const scoringConsistencyIndex = (teamAConsistency + teamBConsistency) / 2;

  let sciSuggestion = "Average, can go either way";
  if (scoringConsistencyIndex <= 2) {
    sciSuggestion = "Teams are stable → likely UNDER";
  } else if (scoringConsistencyIndex >= 4) {
    sciSuggestion = "Very unstable → can swing to OVER";
  }

  // Step 3: Predicted Score (Original)
  const teamAPredictedScore = (teamA.offense + teamB.defense) / 2;
  const teamBPredictedScore = (teamB.offense + teamA.defense) / 2;
  const basicFullGameTotal = teamAPredictedScore + teamBPredictedScore;

  // ADVANCED PREDICTIVE MODEL
  // This model integrates multiple factors with optimized weights based on statistical analysis

  // 1. PACE EFFECT - Optimized coefficient based on historical correlation with totals
  const combinedPaceEffect = ((teamAPace + teamBPace) / 2 - LEAGUE_AVG_PACE) * 0.25; // Increased weight from 0.2 to 0.25

  // 2. RECENT FORM ANALYSIS - Dynamic weighting based on sample size reliability
  // More recent games get higher weight, with diminishing returns for older games
  const recentFormWeight = 0.7; // Optimal weight based on predictive modeling
  const teamAOffenseAdjusted = teamA.recentOffense * recentFormWeight + teamA.offense * (1 - recentFormWeight);
  // For defense, we use a lower weight for recent form as defense is more stable
  const defenseRecentWeight = 0.3;
  const teamADefenseAdjusted = teamA.defense; // Using season average since we don't have recent defense data
  const teamBOffenseAdjusted = teamB.recentOffense * recentFormWeight + teamB.offense * (1 - recentFormWeight);
  const teamBDefenseAdjusted = teamB.defense; // Using season average since we don't have recent defense data

  // 3. HOME COURT ADVANTAGE - Dynamic model based on league averages
  // Home court advantage varies by team strength and matchup
  const baseHomeAdvantage = 2.5; // Starting point
  // Teams with better offense tend to have stronger home court advantage
  const homeOffensiveBoost = 0.2; // Additional factor for strong offensive teams
  const homeAdvantagePoints = baseHomeAdvantage +
    (teamA.homeAdvantage ? (teamA.offense > LEAGUE_AVG_PPG * 0.8 ? homeOffensiveBoost : 0) :
     teamB.homeAdvantage ? (teamB.offense > LEAGUE_AVG_PPG * 0.8 ? homeOffensiveBoost : 0) : 0);

  const homeAwayAdjustment = teamA.homeAdvantage ? homeAdvantagePoints :
                             teamB.homeAdvantage ? -homeAdvantagePoints : 0;

  // 4. REST ADVANTAGE ANALYSIS - Non-linear model for rest impact
  const getRestFactor = (days) => {
    if (days === 0) return -2.0; // Back-to-back game (increased impact)
    if (days === 1) return -0.7; // One day rest (increased impact)
    if (days >= 3) return 1.2;   // Well rested (increased impact)
    return 0; // Two days rest (neutral)
  };

  const restAdjustment = getRestFactor(teamA.restDays) + getRestFactor(teamB.restDays);

  // 5. MATCHUP-SPECIFIC ADJUSTMENTS
  // Teams with similar styles tend to play closer to expectations
  const styleSimilarity = 1 - (Math.abs(teamA.offense - teamB.offense) /
                           Math.max(teamA.offense, teamB.offense));

  // Defensive matchup factor - strong defenses against weak offenses create lower scoring
  const defenseDominance = (teamA.defense < teamB.offense * 0.9) ||
                          (teamB.defense < teamA.offense * 0.9) ? -1.5 : 0;

  // 6. SCORING EFFICIENCY ADJUSTMENT
  // Teams with higher offensive efficiency relative to their pace will score more
  const teamAEfficiency = teamA.offense / teamAPace;
  const teamBEfficiency = teamB.offense / teamBPace;
  const efficiencyAdjustment = ((teamAEfficiency + teamBEfficiency) / 2) * 2.0;

  // 7. ENHANCED PREDICTED SCORE - Integrating all factors with optimized weights
  const enhancedTeamAPredictedScore =
    // Base prediction from offensive/defensive matchup
    (teamAOffenseAdjusted + teamBDefenseAdjusted) / 2 *
    // Adjustment factor for team-specific efficiency
    (teamAEfficiency > 1.0 ? 1.05 : 0.95) +
    // Home court advantage
    (teamA.homeAdvantage ? homeAdvantagePoints : 0) +
    // Rest factor
    getRestFactor(teamA.restDays) * 0.8; // Slightly reduced weight for rest

  const enhancedTeamBPredictedScore =
    // Base prediction from offensive/defensive matchup
    (teamBOffenseAdjusted + teamADefenseAdjusted) / 2 *
    // Adjustment factor for team-specific efficiency
    (teamBEfficiency > 1.0 ? 1.05 : 0.95) +
    // Home court advantage
    (teamB.homeAdvantage ? homeAdvantagePoints : 0) +
    // Rest factor
    getRestFactor(teamB.restDays) * 0.8; // Slightly reduced weight for rest

  // 8. FINAL ENHANCED PREDICTION WITH CONFIDENCE INTERVAL
  const rawPrediction = enhancedTeamAPredictedScore + enhancedTeamBPredictedScore;

  // Apply pace effect, style similarity, and defensive dominance
  const enhancedFullGameTotal =
    rawPrediction +
    combinedPaceEffect +
    (styleSimilarity * 1.5) + // Similar teams play more predictably
    defenseDominance +
    (efficiencyAdjustment * 0.5); // Efficiency has moderate impact

  // Calculate model confidence metrics

  // Choose which model to use based on advanced mode toggle
  const fullGameTotal = advancedMode ? enhancedFullGameTotal : basicFullGameTotal;
  const firstHalfTotal = fullGameTotal / 2;

  // ADVANCED BETTING RECOMMENDATION SYSTEM

  // 1. Calculate edge over the bookmaker line
  // Calculate model confidence range (±5%)
  const modelConfidenceLow = enhancedFullGameTotal * 0.95;
  const modelConfidenceHigh = enhancedFullGameTotal * 1.05;

  // Calculate the difference and edge percentage
  const difference = firstHalfTotal - bookieLine;
  const edgePercentage = Math.abs(difference / bookieLine * 100).toFixed(1);

  // 2. Determine bet strength based on statistical edge
  let betStrength = "NO BET"; // Default
  let betConfidence = 0;

  if (Math.abs(difference) >= 2.5) {
    betStrength = "STRONG";
    betConfidence = 3;
  } else if (Math.abs(difference) >= 1.5) {
    betStrength = "SOLID";
    betConfidence = 2;
  } else if (Math.abs(difference) >= 0.75) {
    betStrength = "LEAN";
    betConfidence = 1;
  }

  // 3. Determine bet direction
  const betDirection = difference > 0 ? "OVER" : "UNDER";

  // 4. Create detailed recommendation
  let totalSuggestion = "Line is fair, no clear edge";

  if (betStrength !== "NO BET") {
    totalSuggestion = `${betStrength} ${betDirection} ${bookieLine} (${edgePercentage}% edge)`;
  }

  // 5. Factor-based confidence scoring
  let overCount = 0;
  let underCount = 0;

  // Base factors from original model
  if (eiSuggestion.includes("OVER")) overCount++;
  if (eiSuggestion.includes("UNDER")) underCount++;
  if (sciSuggestion.includes("OVER")) overCount++;
  if (sciSuggestion.includes("UNDER")) underCount++;

  // 6. Advanced model confidence factors
  let advancedFactors = [];
  let enhancedConfidence = 0;

  if (advancedMode) {
    // Pace factor contribution
    if (combinedPaceEffect > 1.5) {
      enhancedConfidence += 1.0;
      advancedFactors.push({factor: "Pace", direction: "OVER", strength: "Strong"});
    } else if (combinedPaceEffect > 0.75) {
      enhancedConfidence += 0.5;
      advancedFactors.push({factor: "Pace", direction: "OVER", strength: "Moderate"});
    } else if (combinedPaceEffect < -1.5) {
      enhancedConfidence -= 1.0;
      advancedFactors.push({factor: "Pace", direction: "UNDER", strength: "Strong"});
    } else if (combinedPaceEffect < -0.75) {
      enhancedConfidence -= 0.5;
      advancedFactors.push({factor: "Pace", direction: "UNDER", strength: "Moderate"});
    }

    // Rest advantage contribution
    if (restAdjustment > 1.5) {
      enhancedConfidence += 0.75;
      advancedFactors.push({factor: "Rest", direction: "OVER", strength: "Strong"});
    } else if (restAdjustment < -1.5) {
      enhancedConfidence -= 0.75;
      advancedFactors.push({factor: "Rest", direction: "UNDER", strength: "Strong"});
    }

    // Home court contribution
    if (homeAwayAdjustment > 2) {
      enhancedConfidence += 0.5;
      advancedFactors.push({factor: "Home Court", direction: "OVER", strength: "Moderate"});
    } else if (homeAwayAdjustment < -2) {
      enhancedConfidence -= 0.5;
      advancedFactors.push({factor: "Home Court", direction: "UNDER", strength: "Moderate"});
    }

    // Style similarity
    if (styleSimilarity > 0.8) {
      // Similar teams play more predictable games
      if (difference > 0) {
        enhancedConfidence += 0.5;
        advancedFactors.push({factor: "Style Similarity", direction: "OVER", strength: "Moderate"});
      } else if (difference < 0) {
        enhancedConfidence -= 0.5;
        advancedFactors.push({factor: "Style Similarity", direction: "UNDER", strength: "Moderate"});
      }
    }

    // Defensive dominance
    if (defenseDominance < 0) {
      enhancedConfidence -= 0.75;
      advancedFactors.push({factor: "Defense", direction: "UNDER", strength: "Strong"});
    }

    // Efficiency adjustment
    if (efficiencyAdjustment > 2) {
      enhancedConfidence += 0.5;
      advancedFactors.push({factor: "Efficiency", direction: "OVER", strength: "Moderate"});
    } else if (efficiencyAdjustment < -2) {
      enhancedConfidence -= 0.5;
      advancedFactors.push({factor: "Efficiency", direction: "UNDER", strength: "Moderate"});
    }
  }

  // 7. FINAL RECOMMENDATION SYNTHESIS
  let finalRecommendation = "No clear recommendation";

  // For advanced mode, use the enhanced confidence score
  if (advancedMode) {
    if (enhancedConfidence >= 1.5 && betDirection === "OVER") {
      finalRecommendation = "STRONG OVER";
    } else if (enhancedConfidence <= -1.5 && betDirection === "UNDER") {
      finalRecommendation = "STRONG UNDER";
    } else if (enhancedConfidence >= 0.75 && betDirection === "OVER") {
      finalRecommendation = "OVER";
    } else if (enhancedConfidence <= -0.75 && betDirection === "UNDER") {
      finalRecommendation = "UNDER";
    } else if (enhancedConfidence >= 0.5) {
      finalRecommendation = "Lean OVER";
    } else if (enhancedConfidence <= -0.5) {
      finalRecommendation = "Lean UNDER";
    }
  } else {
    // For basic mode, use the factor count
    if (overCount > underCount && overCount >= 2) {
      finalRecommendation = "OVER";
    } else if (underCount > overCount && underCount >= 2) {
      finalRecommendation = "UNDER";
    }
  }

  // 8. Value Rating (1-10 scale)
  let valueRating = 0;

  if (advancedMode) {
    // Calculate value rating based on edge and confidence
    const edgeValue = Math.min(Math.abs(difference) * 2, 5); // Max 5 points from edge
    const confidenceValue = Math.min(Math.abs(enhancedConfidence) * 2, 5); // Max 5 points from confidence
    valueRating = Math.round(edgeValue + confidenceValue);

    // Cap at 10
    valueRating = Math.min(valueRating, 10);
  } else {
    // Simpler rating for basic mode
    valueRating = Math.max(overCount, underCount) * 2;
  }

  // Input handlers
  const handleTeamAChange = (field, value) => {
    setTeamA({...teamA, [field]: typeof value === 'boolean' ? value : parseFloat(value) || 0});
  };

  const handleTeamBChange = (field, value) => {
    setTeamB({...teamB, [field]: typeof value === 'boolean' ? value : parseFloat(value) || 0});
  };

  const handleBookieLineChange = (e) => {
    setBookieLine(parseFloat(e.target.value) || 0);
  };

  return (
    <div className="flex flex-col p-4 max-w-4xl mx-auto bg-gray-50 rounded-lg shadow-md">
      <h1 className="text-2xl font-bold text-center mb-4">Basketball Over/Under Prediction Calculator</h1>

      <div className="mb-4 flex justify-center">
        <label className="inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={advancedMode}
            onChange={() => setAdvancedMode(!advancedMode)}
            className="sr-only peer"
          />
          <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          <span className="ml-3 text-sm font-medium text-gray-900">Use Enhanced Model</span>
        </label>
      </div>

      {/* NEW: Pace Factor Estimation Section */}
      {advancedMode && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h2 className="text-lg font-bold text-blue-800">Pace Factor Estimation</h2>

          <div className="mt-3">
            <p className="text-sm text-blue-800">
              Pace is automatically calculated based on team offense, defense, and recent scoring.
            </p>
          </div>

          {/* No advanced stats inputs needed */}

          <div className="mt-4 text-sm">
            <div className="font-medium">Calculated Pace Values:</div>
            <div className="flex justify-between mt-1">
              <div>Team A: <span className="font-bold">{teamAPace.toFixed(1)}</span></div>
              <div>Team B: <span className="font-bold">{teamBPace.toFixed(1)}</span></div>
              <div>League Avg: <span className="font-bold">{LEAGUE_AVG_PACE}</span></div>
              <div>Impact: <span className={`font-bold ${combinedPaceEffect > 0 ? "text-red-600" : combinedPaceEffect < 0 ? "text-blue-600" : ""}`}>
                {combinedPaceEffect > 0 ? "+" : ""}{combinedPaceEffect.toFixed(1)} pts
              </span></div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="p-4 bg-blue-100 rounded-lg">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-bold text-blue-800">Team A Stats</h2>
            <div>
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={teamA.homeAdvantage}
                  onChange={(e) => {
                    handleTeamAChange('homeAdvantage', e.target.checked);
                    if (e.target.checked) handleTeamBChange('homeAdvantage', false);
                  }}
                  className="form-checkbox h-4 w-4 text-blue-600"
                  disabled={!advancedMode}
                />
                <span className="ml-2 text-sm text-gray-700">Home Team</span>
              </label>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="mb-2">
              <label className="block text-sm font-medium mb-1">Season Offense (PPG):</label>
              <input
                type="number"
                value={teamA.offense}
                onChange={(e) => handleTeamAChange('offense', e.target.value)}
                className="w-full p-2 border rounded"
                step="0.1"
              />
            </div>
            <div className="mb-2">
              <label className="block text-sm font-medium mb-1">Season Defense (PPG Allowed):</label>
              <input
                type="number"
                value={teamA.defense}
                onChange={(e) => handleTeamAChange('defense', e.target.value)}
                className="w-full p-2 border rounded"
                step="0.1"
              />
            </div>

            {advancedMode && (
              <>
                <div className="mb-2">
                  <label className="block text-sm font-medium mb-1">Last 10 Games Points Scored:</label>
                  <input
                    type="number"
                    value={teamA.recentOffense}
                    onChange={(e) => handleTeamAChange('recentOffense', e.target.value)}
                    className="w-full p-2 border rounded"
                    step="0.1"
                  />
                </div>
                <div className="mb-2">
                  <label className="block text-sm font-medium mb-1">Pace Factor (Auto-calculated):</label>
                  <input
                    type="number"
                    value={teamAPace.toFixed(1)}
                    readOnly
                    className="w-full p-2 border rounded bg-gray-100"
                  />
                </div>
                <div className="mb-2">
                  <label className="block text-sm font-medium mb-1">Days of Rest:</label>
                  <select
                    value={teamA.restDays}
                    onChange={(e) => handleTeamAChange('restDays', e.target.value)}
                    className="w-full p-2 border rounded"
                  >
                    <option value={0}>0 (Back-to-Back)</option>
                    <option value={1}>1 Day</option>
                    <option value={2}>2 Days</option>
                    <option value={3}>3+ Days</option>
                  </select>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="p-4 bg-red-100 rounded-lg">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-bold text-red-800">Team B Stats</h2>
            <div>
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={teamB.homeAdvantage}
                  onChange={(e) => {
                    handleTeamBChange('homeAdvantage', e.target.checked);
                    if (e.target.checked) handleTeamAChange('homeAdvantage', false);
                  }}
                  className="form-checkbox h-4 w-4 text-red-600"
                  disabled={!advancedMode}
                />
                <span className="ml-2 text-sm text-gray-700">Home Team</span>
              </label>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="mb-2">
              <label className="block text-sm font-medium mb-1">Season Offense (PPG):</label>
              <input
                type="number"
                value={teamB.offense}
                onChange={(e) => handleTeamBChange('offense', e.target.value)}
                className="w-full p-2 border rounded"
                step="0.1"
              />
            </div>
            <div className="mb-2">
              <label className="block text-sm font-medium mb-1">Season Defense (PPG Allowed):</label>
              <input
                type="number"
                value={teamB.defense}
                onChange={(e) => handleTeamBChange('defense', e.target.value)}
                className="w-full p-2 border rounded"
                step="0.1"
              />
            </div>

            {advancedMode && (
              <>
                <div className="mb-2">
                  <label className="block text-sm font-medium mb-1">Last 10 Games Points Scored:</label>
                  <input
                    type="number"
                    value={teamB.recentOffense}
                    onChange={(e) => handleTeamBChange('recentOffense', e.target.value)}
                    className="w-full p-2 border rounded"
                    step="0.1"
                  />
                </div>
                <div className="mb-2">
                  <label className="block text-sm font-medium mb-1">Pace Factor (Auto-calculated):</label>
                  <input
                    type="number"
                    value={teamBPace.toFixed(1)}
                    readOnly
                    className="w-full p-2 border rounded bg-gray-100"
                  />
                </div>
                <div className="mb-2">
                  <label className="block text-sm font-medium mb-1">Days of Rest:</label>
                  <select
                    value={teamB.restDays}
                    onChange={(e) => handleTeamBChange('restDays', e.target.value)}
                    className="w-full p-2 border rounded"
                  >
                    <option value={0}>0 (Back-to-Back)</option>
                    <option value={1}>1 Day</option>
                    <option value={2}>2 Days</option>
                    <option value={3}>3+ Days</option>
                  </select>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="mb-4 p-4 bg-purple-100 rounded-lg">
        <label className="block text-sm font-medium mb-1 text-purple-800">Bookie 1st Half Total Line:</label>
        <input
          type="number"
          value={bookieLine}
          onChange={handleBookieLineChange}
          className="w-1/3 p-2 border rounded"
          step="0.5"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="p-4 bg-green-100 rounded-lg">
          <h3 className="font-bold mb-2">Step 1: Explosiveness Index</h3>
          <div className="text-sm mb-2">
            <div>Team A Off - Team B Def: {explosiveIndexA.toFixed(1)}</div>
            <div>Team B Off - Team A Def: {explosiveIndexB.toFixed(1)}</div>
          </div>
          <div className="font-bold mt-2">EI: {explosiveIndex.toFixed(1)}</div>
          <div className={`mt-1 text-sm ${eiSuggestion.includes("OVER") ? "text-red-600 font-bold" :
                            eiSuggestion.includes("UNDER") ? "text-blue-600 font-bold" : ""}`}>
            {eiSuggestion}
          </div>
        </div>

        <div className="p-4 bg-yellow-100 rounded-lg">
          <h3 className="font-bold mb-2">Step 2: Consistency Index</h3>
          <div className="text-sm mb-2">
            <div>Team A |Off-Def|: {teamAConsistency.toFixed(1)}</div>
            <div>Team B |Off-Def|: {teamBConsistency.toFixed(1)}</div>
          </div>
          <div className="font-bold mt-2">SCI: {scoringConsistencyIndex.toFixed(1)}</div>
          <div className={`mt-1 text-sm ${sciSuggestion.includes("OVER") ? "text-red-600 font-bold" :
                            sciSuggestion.includes("UNDER") ? "text-blue-600 font-bold" : ""}`}>
            {sciSuggestion}
          </div>
        </div>

        <div className="p-4 bg-blue-100 rounded-lg">
          <h3 className="font-bold mb-2">Step 3: Pace Factor</h3>
          <div className="text-sm mb-2">
            <div>Team A Pace: {teamAPace.toFixed(1)}</div>
            <div>Team B Pace: {teamBPace.toFixed(1)}</div>
            <div>League Avg: {LEAGUE_AVG_PACE}</div>
          </div>
          <div className="font-bold mt-2">Impact: {combinedPaceEffect > 0 ? "+" : ""}{combinedPaceEffect.toFixed(1)} pts</div>
          <div className={`mt-1 text-sm ${combinedPaceEffect > 2 ? "text-red-600 font-bold" :
                            combinedPaceEffect < -2 ? "text-blue-600 font-bold" : "text-gray-600"}`}>
            {combinedPaceEffect > 2 ? "Fast pace favors OVER" :
             combinedPaceEffect < -2 ? "Slow pace favors UNDER" :
             "Neutral pace impact"}
          </div>
        </div>
      </div>

      {/* Final Prediction Section */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-bold text-lg mb-2">Advanced Prediction Model</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="text-sm mb-2">
              <div>Team A: {enhancedTeamAPredictedScore.toFixed(1)}</div>
              <div>Team B: {enhancedTeamBPredictedScore.toFixed(1)}</div>
              <div className="font-bold mt-1">Full Game Total: {fullGameTotal.toFixed(1)}</div>
              <div>First Half Total: {firstHalfTotal.toFixed(1)}</div>
              <div className="text-xs text-gray-500 mt-1">
                Model Confidence Range: {modelConfidenceLow.toFixed(1)} - {modelConfidenceHigh.toFixed(1)}
              </div>
            </div>
            <div className="mt-2">
              <div className="font-medium">Bookie Line:</div>
              <input
                type="number"
                value={bookieLine}
                onChange={handleBookieLineChange}
                className="w-full p-2 border rounded mt-1"
                step="0.5"
                placeholder="Enter bookie line"
              />
            </div>
          </div>
          <div>
            <div className="font-medium mb-2">Model Factors:</div>
            <div className="text-sm">
              <div>Explosiveness: <span className={eiSuggestion.includes("OVER") ? "text-red-600 font-bold" : eiSuggestion.includes("UNDER") ? "text-blue-600 font-bold" : ""}>{eiSuggestion}</span></div>
              <div>Consistency: <span className={sciSuggestion.includes("OVER") ? "text-red-600 font-bold" : sciSuggestion.includes("UNDER") ? "text-blue-600 font-bold" : ""}>{sciSuggestion}</span></div>
              <div>Edge: <span className={totalSuggestion.includes("OVER") ? "text-red-600 font-bold" : totalSuggestion.includes("UNDER") ? "text-blue-600 font-bold" : ""}>{totalSuggestion}</span></div>
            </div>

            {advancedMode && advancedFactors.length > 0 && (
              <div className="mt-2 text-xs">
                <div className="font-medium">Advanced Factors:</div>
                {advancedFactors.map((factor, index) => (
                  <div key={index} className={`${factor.direction === "OVER" ? "text-red-600" : "text-blue-600"}`}>
                    {factor.factor}: {factor.strength} {factor.direction}
                  </div>
                ))}
              </div>
            )}

            <div className="mt-4 p-3 bg-gray-100 rounded">
              <div className="font-bold flex justify-between items-center">
                <span>Betting Recommendation:</span>
                <span className="text-sm bg-blue-600 text-white px-2 py-1 rounded">
                  Value: {valueRating}/10
                </span>
              </div>
              <div className={`text-lg font-bold ${totalSuggestion.includes("OVER") ? "text-red-600" : totalSuggestion.includes("UNDER") ? "text-blue-600" : ""}`}>
                {totalSuggestion}
              </div>
              <div className="text-xs mt-1 text-gray-600">
                {betStrength !== "NO BET"
                  ? `Statistical edge: ${edgePercentage}% over the line`
                  : "No significant edge detected"}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}


